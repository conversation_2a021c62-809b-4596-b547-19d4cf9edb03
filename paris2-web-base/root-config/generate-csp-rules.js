const ENV = process.env.ENV || 'dev2';
const path = require('path');
const fs = require('fs');

const distPath = path.resolve(__dirname, 'csp-check', ENV);

// create dist directory if it doesn't exist
if (!fs.existsSync(distPath)) {
    fs.mkdirSync(distPath,
        { recursive: true });
}

const baseCspRules = require('./csp-rules/base.json');
const envCspRules = require(`../csp.json`);

const cspRules = Object.entries(baseCspRules).reduce((m, [key, rules]) => {
    return {
        ...m,
        [key]: rules.concat(envCspRules[key] ? envCspRules[key] : []),
    };
}, {});

fs.writeFileSync(path.resolve(distPath, 'csp.json'), JSON.stringify(cspRules, null, 2));