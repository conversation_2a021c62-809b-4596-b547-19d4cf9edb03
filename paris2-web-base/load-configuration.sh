#!/bin/bash

# this script is mainly for <PERSON> to download the file
# You can checkout https://bitbucket.org/fleetshipteam/paris2-configuration for local development

COMMIT_ID=$(curl -s --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/commits/$CONFIG_BRANCH?limit=1" | jq -r '.values[0].hash')
curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/paris2-configuration.json"
curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/csp.json"

add_env()
{
  echo $1=$(jq -r ".$2" ./paris2-configuration.json) >> paris2-configuration.env
}
echo '' > paris2-configuration.env
add_env BASE_URL web_app.base_url
add_env SCRIPT_BASE_URL web_app.script_base_url
add_env PARIS_TWO_URL web_app.base_url
add_env SHIP_PARTY_HOST api.base_urls.ship_party
add_env VESSEL_HOST api.base_urls.vessel
add_env KEYCLOCK_HOST api.base_urls.keycloak
add_env TABLEAU_PROXY_HOST api.base_urls.tableau_proxy
add_env SITE_NAME web_app.share_point_cms_site_name
add_env OWNER_PREFERENCES_HOST api.base_urls.vessel_accounting_facade
add_env NOTIFICATION_HOST api.base_urls.notification
add_env REFERENCE_HOST api.base_urls.reference
add_env PARIS_ONE_HOST paris1.host
add_env PARIS_TWO_HOST web_app.base_url
add_env FILE_API_HOST api.base_urls.file
add_env LMS_URL api.base_urls.lms_host
add_env SEAFARER_API_HOST api.base_urls.seafarer
add_env PARIS_TWO_API_HOST api.base_urls.paris_api
add_env FLEETSHIP_URL web_app.fleetship_url

add_env NPM_CDN_PATH web_app.cdn_base_url
add_env REALM auth.keycloak_realm
add_env AUTH_CLIENT_ID auth.client_id

add_env KEYCLOAK_ADMIN_URL api.base_urls.keycloak
add_env RSS_URL web_app.rss_url

add_env GA_MEASUREMENT_ID external_services.google_analytics.measurement_id
add_env GA_CODE external_services.google_analytics.ga_code
add_env UA_CODE external_services.google_analytics.ua_code
add_env GTM_CODE external_services.google_analytics.gtm_code
add_env GOOGLE_API_KEY external_services.google_map.api_key
add_env TABLEAU_PROXY_SERVER_HOST tableau.proxy_host

echo AUTH_SERVER_URL=$(jq -r ".api.base_urls.auth" ./paris2-configuration.json)/ >> paris2-configuration.env
