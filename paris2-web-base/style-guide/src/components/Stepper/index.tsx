import React, { useState, useRef, useEffect } from "react";
import { Card, Col, Container, Row } from "react-bootstrap";
import StepperCard from "./StepperCard";
import BottomButton, { ButtonConfig } from "./BottomButton";
import { CloseIcon } from "./svgIcons";
import Loader from "./Loader";
import '../styles/stepper-style.scss';

/**
 * Configuration for each step in the stepper.
 */
export interface StepConfig {
  label: string; // Step label shown in the stepper
  component: React.ReactNode; // Component to render for this step
  validate?: () => boolean | Promise<boolean>; // Optional validation function for this step
}

/**
 * Test IDs for easier testing and querying DOM elements.
 */
interface TestIds {
  closeBtn?: string;
  stepper?: string;
  card?: string;
  secondaryBtn?: string;
  primaryBtn?: string;
}

/**
 * Props for the Stepper component.
 *
 * @property steps - Array of step configurations.
 * @property onNext - Callback when moving to the next step.
 * @property breadCrumbTitle - Optional breadcrumb title displayed at the top.
 * @property additionalButtons - Additional buttons to show at the bottom.
 * @property primaryBtnTitle - Title for the primary button or a function to generate it.
 * @property secondaryBtnTitle - Title for the secondary button.
 * @property secondaryBtnOnClick - Callback for secondary button click.
 * @property primaryBtnOnClick - Callback for primary button click (final step).
 * @property primaryBtnDisabled - Disable state for primary button.
 * @property secondaryBtnDisabled - Disable state for secondary button.
 * @property onClose - Callback for close button.
 * @property isPreview - If true, shows previewComponent instead of step content.
 * @property previewComponent - Component to show in preview mode.
 * @property onStepChange - Callback when step changes.
 * @property defaultLoadStep - Initial step to load (default is 1).
 * @property customStepper - Custom stepper component to use instead of default.
 * @property testIds - Test IDs for querying elements.
 * @property showLoader - Whether to show loader overlay during async actions.
 */
interface StepperProps {
  steps: StepConfig[];
  primaryBtnOnClick: () => Promise<void> | void;
  primaryBtnTitle:
    | string
    | ((currentStep: number, totalSteps: number) => string);
  primaryBtnDisabled?: boolean;
  onNext?: (currentStep: number) => Promise<void> | void;
  breadCrumbTitle?: string;
  additionalButtons?: ButtonConfig[];
  secondaryBtnTitle?: string;
  secondaryBtnOnClick?: (currentStep: number) => void | Promise<void>;
  secondaryBtnDisabled?: boolean;
  onClose?: () => void;
  isPreview?: boolean;
  previewComponent?: React.ReactNode;
  onStepChange?: (currentStep: number, previousStep: number) => void;
  defaultLoadStep?: number;
  customStepper?: React.ReactNode;
  testIds?: TestIds;
  showLoader?: boolean;
}

/**
 * Stepper
 *
 * A multi-step form component with navigation, validation, and customizable buttons.
 */
const Stepper: React.FC<StepperProps> = ({
  steps,
  onNext,
  breadCrumbTitle,
  additionalButtons = [],
  primaryBtnTitle = "Next",
  secondaryBtnTitle = "Cancel",
  primaryBtnDisabled = false,
  secondaryBtnDisabled = false,
  secondaryBtnOnClick,
  primaryBtnOnClick,
  onClose,
  isPreview = false,
  previewComponent,
  onStepChange,
  defaultLoadStep,
  customStepper,
  testIds = {},
  showLoader = false,
}) => {
  // Current step index (1-based)
  const [step, setStep] = useState(defaultLoadStep ?? 1);
  // Loading state for async actions
  const [loading, setLoading] = useState(false);
  // Ref for scrolling card to top on step change
  const cardRef = useRef<HTMLDivElement>(null);

  // Scroll to top of card on step or preview change
  useEffect(() => {
    if (cardRef.current) {
      cardRef.current.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [step, isPreview]);

  /**
   * Handles step change, calls onStepChange callback if provided.
   */
  const handleStepsChange = async (newStep: number) => {
    const prevStep = step;
    setStep(newStep);
    if (onStepChange) await onStepChange(newStep, prevStep);
  };

  /**
   * Handles next button click:
   * - Validates current step if validate function is provided.
   * - Calls onNext callback if moving to next step.
   * - Calls primaryBtnOnClick if on last step.
   */
  const handleNext = async () => {
    setLoading(true);
    const currentStepConfig = steps[step - 1];
    if (currentStepConfig?.validate) {
      const isValid = await currentStepConfig.validate();
      if (!isValid) {
        setLoading(false);
        return;
      }
    }
    if (step < steps.length) {
      if (onNext) await onNext(step);
      setStep(step + 1);
    } else if (step === steps.length) {
      if (primaryBtnOnClick) await primaryBtnOnClick();
    }
    setLoading(false);
  };

  return (
    <>
      {/* Loader overlay during async actions */}
      {loading && showLoader && <Loader isOverlayLoader />}
      {/* Breadcrumb and close button */}
      {breadCrumbTitle && (
        <div className="stepper-breadcrumb-container">
          <span className="stepper-breadcrumb-title">{breadCrumbTitle}</span>
          <button
            type="button"
            aria-label="Close"
            onClick={onClose}
            className="stepper-close-btn"
            data-testid={testIds.closeBtn}
          >
            <CloseIcon />
          </button>
        </div>
      )}
      <Container fluid className="mt-3">
        <Row>
          {/* Stepper navigation (hidden in preview mode) */}
          {!isPreview && (
            <Col md={4} lg={3} className="pe-3">
              {customStepper ? (
                customStepper
              ) : (
                <StepperCard
                  steps={steps}
                  currentStep={step}
                  setStep={handleStepsChange}
                  data-testid={testIds.stepper}
                />
              )}
            </Col>
          )}
          {/* Main card with step content or preview */}
          <Col md={isPreview ? 12 : 8} lg={isPreview ? 12 : 9}>
            <Card
              ref={cardRef}
              className="stepper-card-main"
              data-testid={testIds.card}
            >
              {isPreview ? previewComponent : steps[step - 1]?.component}
            </Card>
          </Col>
        </Row>
      </Container>
      {/* Bottom buttons: additional, secondary, primary */}
      <BottomButton
        buttons={[
          ...additionalButtons,
          {
            title: secondaryBtnTitle,
            testID: testIds.secondaryBtn ?? "stepper-sec-btn",
            variant: "secondary",
            customClass: "stepper-btm-sec-btn",
            onClick: secondaryBtnOnClick
              ? () => secondaryBtnOnClick(step)
              : undefined,
            disabled: secondaryBtnDisabled,
          },
          {
            title:
              typeof primaryBtnTitle === "function"
                ? primaryBtnTitle(step, steps.length)
                : primaryBtnTitle,
            testID: testIds.primaryBtn ?? "stepper-prim-btn",
            variant: "primary",
            customClass: "primary-btn fs-14 btn-secondary-bg",
            onClick: () => {
              // eslint-disable-next-line no-void
              void handleNext();
            },
            disabled: primaryBtnDisabled,
          },
        ]}
      />
    </>
  );
};

export default Stepper;
