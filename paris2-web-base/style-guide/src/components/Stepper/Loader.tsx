import classNames from 'classnames';
import React from 'react';
/**
 * The Loader component renders a loading indicator with optional overlay styling based on the provided
 * props.
 * @param {IProps} props - The `Loader` component accepts two optional props:
 * @returns The Loader component is being returned, which is a div element containing a child div
 * element with the class "border-5 primary-txt-color" and the data-testid attribute set to
 * "defaultLoader". The parent div has dynamic classes based on the props passed to the component,
 * including the className prop and the isOverlayLoader prop which conditionally adds the class
 * "stepper-overlay-loader" which make the loader occupy the entire viewport.
 */

interface IProps {
  isOverlayLoader?: boolean;
  className?: string;
}

const Loader = (props: IProps) => {
  const {className, isOverlayLoader} = props;

  return (
    <div
      className={classNames(
        'w-full h-full flex-1 d-flex justify-content-center align-items-center',
        className,
        {'stepper-overlay-loader': isOverlayLoader},
      )}
    >
      <div
        className="border-5 primary-txt-color"
        data-testid="defaultLoader"
      ></div>
    </div>
  );
};

export default Loader;
