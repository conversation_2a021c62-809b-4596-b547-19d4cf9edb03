worker_processes 1;

events { worker_connections 1024; }

http {
    server {
        listen 80;
        server_name paris2-dev2.fleetship.com;

        return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl;
        server_name paris2-dev2.fleetship.com;

        root /usr/share/nginx/html;
        index index.html;

        ssl_certificate /etc/nginx/certs/nginx-selfsigned.crt;
        ssl_certificate_key /etc/nginx/certs/nginx-selfsigned.key;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location ~* \.(js|css|svg|jpeg|jpg|ico|woff|woff2|ttf|otf|json|png)$ {
            proxy_pass https://d3p7wog5f1f0xz.cloudfront.net;
            proxy_ssl_protocols TLSv1.2;
            proxy_ssl_server_name on;
            proxy_ssl_name paris2-dev2.fleetship.com;
            proxy_set_header  Host              $host;
            proxy_set_header  X-Real-IP         $remote_addr;
            proxy_set_header  X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header  X-Forwarded-Proto $scheme;
            proxy_pass_header Authorization;
        }
    }
}