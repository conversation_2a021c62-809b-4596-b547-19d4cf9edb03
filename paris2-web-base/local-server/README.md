This folder contain the Nginx config for testing index.html locally.

Before this please build the index.html in `/root-config/dist/dev2/index.html`

1. Generate a local cert by:

```sh
ENV=dev2 ./generate-cert.sh
```

After the cert is generated, you need to trust the cert on your machine. The steps will depend on the OS and browsers.


2. run `ENV=dev2 CONFIG_BRANCH=develop ./launch.sh`

3. Open host file

```
sudo vi /etc/hosts
```

4. add the follow line:

```
127.0.0.1 paris2-dev2.fleetship.com
```

## How to rebuild index.html

```sh
cd ../root-config
ENV=dev2 npm run build
```