This folder contain the Nginx config for testing index.html locally.

Before this please build the index.html in `/root-config/dist/dev2/index.html`

## Guide for MacOS

1. Generate a local cert by:

```sh
ENV=dev2 ./generate-cert.sh
```

After the cert is generated, you need to trust the cert on your machine. The steps will depend on the OS and browsers.


2. run `ENV=dev2 CONFIG_BRANCH=develop ./launch.sh`

3. Open host file

```
sudo vi /etc/hosts
```

4. add the follow line:

```
127.0.0.1 paris2-dev2.fleetship.com
```

## Guide for Windows + git bash
This section is the guide to use Windows + git bash to connect UAT2 environment locally

### What is Git Bash on Windows?
Git Bash is a command-line interface (CLI) that comes bundled with Git for Windows. It’s essentially a lightweight emulation of a UNIX-like shell environment, specifically based on the Bash shell (Bourne Again Shell) commonly found in Linux and macOS systems. When you install Git for Windows, Git Bash is included as an option to provide a familiar terminal experience for users accustomed to UNIX-style commands.

### Why Use Git Bash to Run UNIX-Based Commands on Windows?
Windows has its own native command-line tools—Command Prompt (cmd.exe) and PowerShell—but they use a different syntax and command set compared to UNIX-based systems. Git Bash bridges this gap.

### Setup

1. install git bash

2. Update the generate-cert.sh, a special syntax handling for git bash
```
#!/bin/bash
mkdir -p certs
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout certs/nginx-selfsigned.key -out certs/nginx-selfsigned.crt \
-subj "//CN=paris2-${ENV}.fleetship.com" -addext "subjectAltName=DNS:paris2-${ENV}.fleetship.com,DNS:paris2-${ENV}.fleetship.com"
```

3. Generate a local cert by:

```sh
ENV=uat2 ./generate-cert.sh
```

After the cert is generated, you need to trust the cert on Windows.
- Press Win+R, open certmgr.msc
- Go to Trusted Root Certification > Certificates > Right click > All tasks > Import
- Browse the cert.crt generated from Step 2
- Click Finish. You’ll see a confirmation if successful.
- Verify if the cert is imported. it should appear under Trusted Root Certification Authorities > Certificates > paris2-${ENV}.fleetship.com

4. update the load-configuration.sh to your bitbucket authentication.

5. update the nginx.conf to your target environment e.g. dev2 to uat2

6. run `ENV=uat2 CONFIG_BRANCH=develop ./launch.sh`

7. Edit host file on Windows
Open the notepad & edit the hosts in the C:\Windows\System32\drivers\etc

Edit & add the follow line:

```
# Uncomment the below line
127.0.0.1       localhost
::1             localhost

# Add this line For PARIS2.0 UAT2:
127.0.0.1 paris2-uat2.fleetship.com
```

8. Open PARIS2.0 UAT2, you should now able to override the module with local single-spa-inspector.

## How to rebuild index.html

```sh
cd ../root-config
ENV=dev2 npm run build
```