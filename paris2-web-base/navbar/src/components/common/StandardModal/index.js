import React from "react";
import { But<PERSON>, Modal } from "react-bootstrap";
import "./style.scss";

// Simple SubmitButton component since it's not available
const SubmitButton = ({ isLoading, children, disabled, ...props }) => (
  <Button {...props} disabled={disabled || isLoading}>
    {isLoading && (
      <span
        className="spinner-border spinner-border-sm me-2"
        aria-hidden="true"
      ></span>
    )}
    {children}
  </Button>
);

const StandardModal = (props) => {
  const {
    visible,
    children,
    title,
    onVisibleChange,
    okText = "Ok",
    cancelText = "Cancel",
    style = {},
    bodyStyle = {},
    okButtonProps,
    cancelButtonProps,
    onOk,
    onCancel,
    confirmLoading = false,
    footer = true,
    size,
    centered,
    closeButton,
  } = props;

  const handleModalHide = () => {
    onVisibleChange?.(!visible);
  };

  const handleOkClick = (e) => {
    onOk?.(e);
  };

  const handleCancelClick = (e) => {
    onCancel?.(e);
    handleModalHide();
  };

  const footerDom = (
    <Modal.Footer style={{ borderTop: "0" }}>
      <SubmitButton
        variant="primary"
        onClick={handleOkClick}
        isLoading={confirmLoading}
        {...okButtonProps}
      >
        {okText}
      </SubmitButton>
      <Button
        variant="secondary"
        onClick={handleCancelClick}
        disabled={confirmLoading}
        size="sm"
        {...cancelButtonProps}
      >
        {cancelText}
      </Button>
    </Modal.Footer>
  );

  return (
    <Modal
      size={size}
      show={visible}
      onHide={handleModalHide}
      aria-labelledby="modal"
      centered={centered}
      style={style}
      className="standard-modal"
      dialogClassName="recent-voyage-dialog"
      backdrop="static"
      keyboard={false}
    >
      <Modal.Header closeButton={closeButton}>
        <Modal.Title
          id="modal"
          className="h5 text-truncate"
          style={{ borderBottom: "0" }}
        >
          {title}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={bodyStyle}>{children}</Modal.Body>
      {footer && footerDom}
    </Modal>
  );
};

export default StandardModal;
