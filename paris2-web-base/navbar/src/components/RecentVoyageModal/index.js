import React, { useState, useEffect } from "react";
import { Alert } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import StandardModal from "../common/StandardModal";
import notificationService from "../../service/notification-service";
import "./style.scss";

const RecentVoyagesModal = () => {
  const navigate = useNavigate();

  const [alertMessage, setAlertMessage] = useState({});
  const [showModal, setShowModal] = useState(false);

  const handleButtonClick = () => {
    notificationService.markReadMessages([alertMessage.id]);
    navigate(alertMessage.link || "/");
    setShowModal(false);
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  useEffect(() => {
    fetchRecentVoyages();
  }, []);

  const fetchRecentVoyages = async () => {
    try {
      const response = await notificationService.getVoyageAlert();

      if (response.message) {
        setAlertMessage(response);
        setShowModal(true);
      }
    } catch (err) {
      console.error("Error fetching recent voyages:", err);
    }
  };

  return (
    <StandardModal
      visible={showModal}
      title="Completion of EU Voyages"
      onVisibleChange={handleModalClose}
      cancelText="Close"
      okText="Review Now"
      onOk={handleButtonClick}
      okButtonProps={{ size: "sm" }}
      cancelButtonProps={{ size: "sm" }}
      centered
    >
      <div className="recent-voyages-modal">
        <Alert className="note-section">{alertMessage.message}</Alert>
      </div>
    </StandardModal>
  );
};

export default RecentVoyagesModal;
