import { createHeaders } from "./user-service";

const { NOTIFICATION_HOST } = process.env;

export const getVoyageAlert = async () => {
  const headers = await createHeaders();
  const urlParams = new URLSearchParams({
    code: "VESSEL_VOYAGE_COMPLETION",
    order: "created_at DESC",
  });
  const response = await fetch(
    `${NOTIFICATION_HOST}/api/alert?${urlParams.toString()}`,
    {
      method: "GET",
      headers,
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Failed to fetch recent EU voyages: ${response.status} - ${errorText}`
    );
  }
  const json = await response.json();

  return json?.message || {};
};
export const markReadMessages = async (messageIds) => {
  const headers = await createHeaders();
  const response = await fetch(`${NOTIFICATION_HOST}/api/messages/mark-read`, {
    method: "POST",
    headers,
    body: JSON.stringify({ messageIds }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Failed to mark messages as read: ${response.status} - ${errorText}`
    );
  }
  return response.json();
};
export default {
  createHeaders,
  getVoyageAlert,
  markReadMessages
};
