{"@paris2/root-config": "/root-config.js", "@paris2/navbar": "/navbar/paris2-navbar.js", "@paris2/styleguide": "/styleguide/paris2-styleguide.js", "@paris2/external-config": "/external-config/external-config.js", "@paris2/localization": "/localization/paris2-localization.js", "@paris2/vessel": "/vessel/paris2-vessel.js", "@paris2/seafarer": "/seafarer/paris2-seafarer.js", "@paris2/notifications": "/notifications/paris2-notifications.js", "@paris2/auth": "/auth/paris2-auth.js", "@paris2/notification-events": "/notification-events/paris2-notification-events.js", "@paris2/item-master": "/item-master/paris2-item-master.js", "@paris2/inspections-audits": "/inspections-audits/paris2-inspections-audits.js", "@paris2/deficiency": "/deficiency/paris2-deficiency.js", "@paris2/quality-management-system": "/quality-management-system/paris2-quality-management-system.js", "@paris2/ship-party": "/ship-party/paris2-ship-party.js", "@paris2/tableau": "/tableau/paris2-tableau.js", "@paris2/nova": "/nova/paris2-nova.js", "@paris2/landing-page": "/landing-page/paris2-landing-page.js", "@paris2/faq": "/faq/paris2-faq.js", "@paris2/import-sheet": "/portage-bill/paris2-import-sheet.js", "@paris2/survey": "/survey/paris2-survey.js", "@paris2/qhse": "/qhse/paris2-qhse.js", "@paris2/owner-reporting": "/owner-reporting/paris2-owner-reporting.js", "@paris2/reference": "/reference/paris2-reference.js", "@paris2/external-reporting": "/external-reporting/paris2-external-reporting.js", "@paris2/pms": "/pms/paris2-pms.js", "@paris2/new-building": "/new-building/paris2-new-building.js", "@paris2/risk-assessment": "/risk-assessment/paris2-risk-assessment.js"}