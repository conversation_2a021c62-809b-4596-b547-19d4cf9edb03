export interface Vessel {
  name: string;
  vesselData: any[];
  type: string; // can be custom for defining the different types of tabs
  vessel_ownership_id?: number;
  risk_id?: number | null;
  vessel_id?: number;
}

export interface VesselGroup {
  title: string;
  //   vessels: string[];
  vessels: VesselOption[];
}

export interface VesselOption {
  vessel_id: number;
  vessel_ownership_id?: number;
  name: string;
}

//replace VesselGroupOption this with VesselGroup in all places
export interface VesselGroupOption {
  title: string;
  vessels: VesselOption[];
}

export interface MultiVesselSelectOptionConfig {
  placeholder: string;
  width: string;
  groups: VesselGroupOption[];
}

export interface IProjectListResponse<T> {
  data: T[];
  pagination: {
    totalItems: number;
    totalPages: number;
    page: number;
    pageSize: number;
  };
}

//for using dry principle
export interface VesselDisplayProps {
  vessels: Vessel[];
  tableHeaders: string[];
  badgeColors: string[];
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  isFetchingNextPage?: boolean;
  isLoading?: boolean;
  fetchNextPage: () => void;
  pagination: IProjectListResponse<any>['pagination'];
}

export interface VesselTableProps extends VesselDisplayProps {
  cellStyleType?: 'default' | 'conditional';
}

export interface VesselGridProps extends VesselDisplayProps {
  isModal: boolean; // Specific prop for the grid view in a modal
  gridComponent?: 'bar' | 'pie' | 'dashboard';
}

export interface MultiVesselSelectConfig {
  placeholder: string;
  width: string;
  groups: VesselGroup[];
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
}

export interface VesselModuleProps {
  title: string;
  vessels: Vessel[];
  tabs: string[];
  tableHeaders: string[];
  badgeColors: string[];
  multiVesselSelects: MultiVesselSelectConfig[];

  // Callbacks
  onRefresh: () => void;
  //   onSendEmail: (vesselName: string) => void;
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  fetchNextPage: () => void;

  // State flags
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  IsVesselSelectVisible?: boolean;
  IsAllTabVisible?: boolean;
  isFetchingNextPage?: boolean;
  isLoading?: boolean;

  // Prop to control layout
  vesselSelectPosition?: 'before' | 'after';

  // Sizing
  containerSize?: {
    width: string;
    height: string;
  };
  modalSize?: {
    width: string;
    height: string;
  };

  pagination: IProjectListResponse<any>['pagination'];
  gridComponent?: 'bar' | 'pie' | 'dashboard';
  defaultComponent?: 'list' | 'grid';
  cellStyleType?: 'default' | 'conditional';
}

export interface VesselSelectItem {
  groups: VesselGroup[]; // Groups for this select
  selectedVessels: string[]; // Selected vessels for this select
  placeholder?: string;
  width?: string;
}

export type VesselSelectGroupProps = {
  index: number;
  config: {
    placeholder?: string;
    width?: string;
  };
  selectedVessels: string[];
  groups: VesselGroup[];
  onChange: (index: number, selected: string[]) => void;
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
};

export interface VesselDropdownProps {
  groups: VesselGroup[];
  selectedVessels: string[];
  onSelectionChange: (selected: string[]) => void;
  placeholder?: string;
  width?: string;
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
}

export interface VesselGroupListProps {
  filteredGroups: VesselGroupOption[];
  selectedVessels: string[];
  onToggleVessel: (vesselName: string) => void;
  onToggleGroup: (group: VesselGroupOption) => void;
}

export interface VesselDropdownMenuProps extends VesselGroupListProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  isAllSelected: boolean;
  onToggleAll: () => void;
  isSearchBoxVisible?: boolean;
  isSelectAllVisible?: boolean;
}

export interface DropdownFooterProps {
  isAllSelected: boolean;
  onToggleAll: () => void;
}

export interface SearchInputProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export interface FetchParams {
  page: number;
  limit: number;
  type?: string;
  [key: string]: any;
}

export interface RiskApprover {
  id: number;
  risk_id: number;
  keycloak_id: string;
  user_name: string;
  user_email: string;
  job_title: string | null;
  message: string | null;
  approval_order: number | null;
  approval_status: number | null;
  approval_date: string | null;
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface RiskAssessment {
  vessel_id: number;
  vessel_name: string;
  vessel_ownership_id: number;
  ra_level: number | null;
  status: number;
  task_requiring_ra: string;
  risk_approver: RiskApprover[];
}

export interface IRiskAssessmentResponse {
  message: string;
  data: RiskAssessment[];
}

export enum RAStatus {
  DRAFT = 1,
  PENDING = 2,
  APPROVED = 3,
  REJECTED = 4,
  APPROVED_WITH_CONDITION = 5,
}

export enum RaLevel {
  ROUTINE = 1,
  SPECIAL = 2,
  CRITICAL = 3,
  LEVEL_1_RA = 4,
  Unassigned = -1, // handle null with -1 internally
}

export interface TransformedRisk {
  name: string;
  type: keyof typeof RaLevel;
  vesselData: [string, string, string]; // task, RA level label, status label
  vessel_ownership_id: number;
  risk_id: number | null; // from risk_approver[0]
}

export interface VesselModuleContainerProps {
  title: string;
  fetchFn: (params: {
    page: number;
    limit: number;
    [key: string]: any;
  }) => Promise<IProjectListResponse<Vessel>>;
  tabs: string[];
  //   multiVesselSelects: MultiVesselSelectConfig[];
  tableHeaders: string[];
  badgeColors: string[];
  containerSize: { width: string; height: string };
  modalSize: { width: string; height: string };
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  IsVesselSelectVisible?: boolean;
  vesselSelectPosition?: 'before' | 'after';
  gridComponent?: 'bar' | 'pie' | 'dashboard';
  defaultComponent?: 'list' | 'grid';
  cellStyleType?: 'default' | 'conditional';
}

export interface UseInfiniteScrollProps {
  fetchNextPage?: () => void;
  isFetchingNextPage?: boolean;
  hasNextPage: boolean;
  dataLength: number; // Used to re-run checks when new data arrives
}
