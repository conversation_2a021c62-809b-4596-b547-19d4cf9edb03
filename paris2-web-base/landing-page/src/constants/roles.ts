const ROLES = {
  VIEW: 'nova|view',
  VESSEL_VIEW: 'vessel|view',
  VESSEL_VIEW_TANKER: 'vessel|view|tanker',
  VESSEL_VIEW_DRY: 'vessel|view|dry',
};

const EXTERNAL_GROUPS = {
  EXTERNAL_OWNER: '/External/Owner',
  EXTERNAL_OWNER_BASIC: '/External/Owner/Basic',
  EXTERNAL_REGISTERED_OWNER: '/External/Registered Owner',
  EXTERNAL_REGISTERED_OWNER_BASIC: '/External/Registered Owner/Basic',
};

export const VesselTypeRoles = [ROLES.VESSEL_VIEW_TANKER, ROLES.VESSEL_VIEW_DRY];
export const OWNER_GROUP_PREFIX = [
  EXTERNAL_GROUPS.EXTERNAL_OWNER,
  EXTERNAL_GROUPS.EXTERNAL_REGISTERED_OWNER,
];

export const KPI_SCORECARD_VIEW = 'analytics|kpiscorecard|view';

export { ROLES };
