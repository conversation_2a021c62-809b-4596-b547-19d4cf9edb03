svg {
  font-family: sans-serif;
  .domain {
    display: none;
  }
  .tick line {
    stroke: #ccc;
  }
  .tick text {
    fill: #858383;
    font-size: 14px !important;
  }
}

.xAxix-legend-parent {
  position: sticky;
  bottom: 0;
  z-index: 10;
  margin-top: -50px;
}

.vessel-bar-chart-container {
  position: relative;
  width: 100%;
  height: 290px; // Fixed height for the container
  display: flex;
  flex-direction: column;

  .custom-tooltip {
    position: fixed;
    background: rgb(0, 0, 0);
    color: white;
    border-radius: 4px;
    padding: 10px;
    font-weight: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s;
    margin-top: -50px;

    &::before {
      content: "";
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
      border: 6px solid transparent;
      border-right-color: black;
      filter: drop-shadow(-2px 0 1px rgba(0, 0, 0, 0.1));
    }

    .tooltip-title {
      font-weight: 300;
    }

    .tooltip-row {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .tooltip-color {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 12px;
    }
  }

  .bar-group {
    cursor: pointer;
  }

  .chart-scrollable {
    flex: 1;
    overflow-y: auto;
    position: relative;

    .main-chart {
      display: block;
      width: 100%;
    }
  }

  .x-axis-container {
    height: 40px;
    background-color: white;
    

    .x-axis {
      display: block;
      width: 100%;
    }
  }

  .tooltip {
    position: absolute;
    padding: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    pointer-events: none;
  }

  // gives us dashed vertical line
  // .x-grid line {
  //   stroke-dasharray: 3,3;
  // }

  .y-axis text {
    font-size: 14px;
    font-weight: 550;
    fill: #1c1919;
  }
}

//below legend
.vessel-bar-chart-legend {
  display: flex;
  gap: 20px;
  padding-left: 220px;
  font-size: 12px;
  background-color: white;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .legend-color {
    width: 14px;
    height: 14px;
    border-radius: 15px;
    display: inline-block;
  }

  .legend-label {
    font-weight: 500;
    color: #333;
  }
}
