@use "./variables" as *;

.ra-vessel-selects-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.ra-vessel-selects-container .ra-selectWrapper {
  flex: 1 1 auto;
  min-width: 200px;
  max-width: 100%;
}

// Vessel Module Container
.ra-vessel-module-container {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
  border-radius: 5.2px;
  background: #ffffff;
  height: var(--container-height, 490px);

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// Header Section
.ra-vessel-module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;

  gap: 1rem;
}

.ra-vessel-module-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: $color-fleet-blue;
}

.ra-vessel-module-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.ra-view-toggle-container {
  background-color: color(display-p3 0.9569 0.9647 0.9725);
  padding-bottom: 0.1rem;
  padding-left: 0.3rem;
  padding-top: 0.3rem;
  border-radius: 0.25rem;
}

.ra-view-toggle-button {
  padding: 0.1rem;
  padding-bottom: 0.01rem !important;
  border-radius: 0.25rem;
  margin-right: 0.3rem;
  border: none;

  &.active {
    background-color: $color-fleet-blue;
    color: white;
  }

  &:not(.active) {
    color: $color-fleet-blue;

    &:hover {
      background-color: #bfdbfe;
    }
  }
}

.ra-view-toggle-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.ra-enlarge-icon {
  width: 1.6rem;
  height: 1.6rem;
  color: $color-fleet-blue;
  margin-left: 0.5rem;
  cursor: pointer;
}

// Last Updated Section
.ra-last-updated-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  margin-top: .5rem;
}

.ra-last-updated-text {
  font-size: 0.9rem;
  font-weight: 200;
  color: #71767e;
  display: flex;
  align-items: center;
  margin-top: -10px;
  margin-bottom: 5px;
}

.ra-refresh-icon {
  width: 1.15rem;
  height: 1.15rem;
  margin-left: 0.3rem;
  color: $color-fleet-blue;
  cursor: pointer;
}

// Tabs Section
.ra-tabs-container {
  display: flex;
  font-weight: 300;
  gap: 1.5rem;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  border-bottom: 1px solid #e5e7eb;

  @media (max-width: 640px) {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

.ra-tab-button {
  position: relative;
  color: #6b7280;
  padding-top: 0.5rem;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  padding-bottom: 0.6rem;

  &.active {
    color: $color-fleet-blue;
    font-weight: 600;
  }
}

.ra-active-tab-indicator {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: $color-fleet-blue;
}

// Content Container
.ra-content-container {
  flex-grow: 1;
  overflow: hidden;
  position: relative;
}

.ra-content-container-modal {
  height: 350px;
}

.ra-content-container-non-modal {
  height: 300px;
}

// Modal Styles
.ra-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  cursor: pointer;
  border: none;
  padding: 0;
}

.ra-modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
  padding: 2.5rem;
  position: relative;
  cursor: auto; /* Shows it's clickable */

  /* Use the CSS variables with fallbacks for default sizes */
  width: var(--modal-width, 700px);
  height: var(--modal-height, 700px);

  @media (max-width: 768px) {
    width: 95vw !important;
    height: 95vh !important;
    padding: 1.5rem;
  }
}

.ra-close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #4b5563;
  cursor: pointer;

  &:hover {
    color: #000;
  }
}