@use "./variables" as *;

button {
  all: unset;
}

.vessel-selects-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.vessel-selects-container .selectWrapper {
  flex: 1 1 auto;
  min-width: 200px;
  max-width: 100%;
}

// Vessel Module Container
.vessel-module-container {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  margin: 1rem 1rem 1rem 0rem;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
  border-radius: 5.2px;
  background: #ffffff;

  /* Use the CSS variables with fallbacks for default sizes */
  width: var(--container-width, 696px);
  height: var(--container-height, 490px);

  @media (max-width: 768px) {
    margin: 1rem;
    padding: 1rem;
  }
}

// Header Section
.vessel-module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // margin-bottom: 1rem;
}

.vessel-module-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: $color-fleet-blue; // fleet-text-blue equivalent
}

.vessel-module-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.view-toggle-container {
  background-color: color(display-p3 0.9569 0.9647 0.9725);
  padding-bottom: 0.1rem;
  padding-left: 0.3rem;
  padding-top: 0.3rem;
  border-radius: 0.25rem; 
}

.view-toggle-button {
  padding: 0.1rem;
  padding-bottom: 0.01rem !important;
  border-radius: 0.25rem; 
  margin-right: 0.3rem;
  border: none;

  &.active {
    background-color: $color-fleet-blue; // fleet-bg-blue equivalent
    color: white;
  }

  &:not(.active) {
    color: $color-fleet-blue; // fleet-text-blue equivalent

    &:hover {
      background-color: #bfdbfe;
    }
  }
}

.view-toggle-icon {
  width: 1.25rem; 
  height: 1.25rem; 
}

.enlarge-icon {
  width: 1.6rem; 
  height: 1.6rem;
  // margin-top: 0.5rem;
  color: $color-fleet-blue;
  margin-left: 0.5rem;
  cursor: pointer;
}

// Last Updated Section
.last-updated-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.last-updated-text {
  font-size: 0.9rem;
  font-weight: 200;
  color: #71767e;
  display: flex;
  align-items: center;
  margin-top: -10px;
  margin-bottom: 5px;
}

.refresh-icon {
  width: 1.15rem;
  height: 1.15rem;
  margin-left: 0.3rem;
  color: $color-fleet-blue;
  cursor: pointer;
}

// Tabs Section
.tabs-container {
  display: flex;
  font-weight: 300;
  gap: 1.5rem;
  margin-bottom: 1rem; 
  font-size: 1.1rem;
  border-bottom: 1px solid #e5e7eb; 

  @media (max-width: 640px) {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

.tab-button {
  position: relative;
  padding-bottom: 0.5rem;
  color: #6b7280;
  padding: 0.6rem;

  &.active {
    color: $color-fleet-blue;
    font-weight: 600;
  }
}

.active-tab-indicator {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: $color-fleet-blue; 
}

// Content Container
.content-container {
  // overflow-y: auto;
  // border-color: #e5e7eb;
  flex-grow: 1; // Allows this container to take up all available vertical space
  overflow: hidden; // Prevents double scrollbars
  position: relative; // Important for child positioning

  &.modal {
    height: 350px;
  }

  &:not(.modal) {
    height: 300px;
  }
}

// Modal Styles
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3); 
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
  padding: 2.5rem;
  position: relative;

  /* Use the CSS variables with fallbacks for default sizes */
  width: var(--modal-width, 700px);
  height: var(--modal-height, 700px);

  @media (max-width: 768px) {
    width: 95vw !important;
    height: 95vh !important;
    padding: 1.5rem;
  }
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #4b5563;
  cursor: pointer;

  &:hover {
    color: #000;
  }
}
