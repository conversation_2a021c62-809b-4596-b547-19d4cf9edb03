@use "./variables" as *;

.tableContainer {
  position: relative;
  overflow-y: auto;
  height: 100%;
}

.table {
  width: 96%;
  border-collapse: collapse;
}

// --- Table Header ---
.tableHeader {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;

  tr {
    font-size: 1.01rem !important;
    color: #000000;
  }

  th {
    padding: 0.5rem 0;
    font-weight: 600;
    cursor: pointer; // Default cursor for sorting
    user-select: none; // Prevent text selection on click
  }
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sortIcon {
  &.neutral {
    color: #a1a1aa; // A neutral grey for the default icon
  }
  &.active {
    color: $color-fleet-blue;
  }
}

.actionHeader {
  // Styles for the non-sortable "Actions" header
  &.nonSortable {
    cursor: default;
  }
}

.textLeft {
  text-align: left;
  .headerContent {
    justify-content: flex-start;
  }
}

// --- Table Body & Rows ---
.tableRow {
  border-top: 1px solid #e5e7eb;
  td {
    padding: 0.5rem 0;
    // text-align: center;
  }
}

.vesselNameCell {
  font-weight: 600;
  text-align: left !important;
}

.vesselNameButton {
  color: $color-fleet-blue;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  font-size: 1rem !important;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.vesselSecondColumnEntry {
  padding: 0.5rem 0;
}

// --- Badge Styling ---

// Default badge used for non-conditional styles
.badge {
  color: var(--badge-text-color, white);
  background-color: var(--badge-bg-color, #808080);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem !important;
  display: inline-block;
}

// Base class for all status badges
.statusBadge {
  display: inline-block;
  padding: 4px 10px !important;
  font-size: 0.9rem;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 5px;
  // Use CSS variable with a fallback. The `rgba` function uses the RGB values from the variable.
  background-color: rgba(var(--status-rgb, 128, 128, 128), 0.1);
  color: rgb(var(--status-rgb, 128, 128, 128));
}

// Modifier classes for specific statuses (using static colors)
.approvedBadge {
  background-color: #e8f5e9; // Light Green
  color: #4caf50; // Green
}

.approvedWithConditionBadge {
  background-color: #f1f8e9; // Lighter Green
  color: #8bc34a; // Light Green
}

.rejectedBadge {
  background-color: #ffebee; // Light Red
  color: #f44336; // Red
}

// --- Email/Action Cell ---
.emailCell {
  text-align: center;
}

.emailButtonWrapper {
  position: relative;
  display: inline-block;

  .emailButton {
    color: $color-fleet-blue;
    cursor: pointer;
    padding: 0.25rem 0;
    background: none;
    border: none;
  }

  .tooltip {
    position: absolute;
    bottom: 125%; // Position above the button
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background: black;
    color: white;
    font-weight: 300;
    font-size: 1.1rem !important;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
    z-index: 10;

    &::after { // Changed from ::before for bottom positioning
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: black;
    }
  }

  &:hover .tooltip {
    opacity: 1;
  }
}

// --- Loading & Status Cells ---
.spinnerContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.statusCell {
  text-align: center;
  padding: 4rem 0;
  font-size: 1rem;
  color: #6b7280;
  height: 200px;
}

// --- Media Queries ---
@media (max-width: 768px) {
  .tableContainer {
    max-height: calc(100vh - 200px) !important;
  }
  .table .tableHeader tr {
    font-size: 0.75rem;
  }
  .table .tableRow .dataCell .badge {
    padding: 0.15rem 0.5rem;
    font-size: 0.65rem;
  }
}
