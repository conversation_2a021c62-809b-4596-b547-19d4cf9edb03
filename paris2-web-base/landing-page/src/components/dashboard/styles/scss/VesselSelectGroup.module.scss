.container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.selectWrapper {
  position: relative;
  flex: 1 1 auto;
}

.select {
  border: 1px solid #d1d5db;
  color: #6b7280;
  appearance: none;
  border-radius: 0.375rem;
  padding-left: 1rem;
  font-size: 1rem;
  width: 100%;
  background-color: white;
  font-weight: 350;
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px #143857;
  }
}

.pyDefault {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

@for $i from 1 through 8 {
  .py-#{$i} {
    padding-top: #{$i * 0.25}rem;
    padding-bottom: #{$i * 0.25}rem;
  }
}

.chevron {
  pointer-events: none;
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}
.smallHeight {
  height: 40px;
}
