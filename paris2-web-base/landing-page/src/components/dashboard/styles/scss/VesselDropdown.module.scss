@use "./variables" as *;

.raDropdownContainer {
  position: relative;
  font-family: Arial, sans-serif;
  font-size: 13px;
  color: #858383;
  width: var(--dropdown-width, 300px);
}

.dropdownWidth--small {
  --dropdown-width: 200px;
}

.dropdownWidth--medium {
  --dropdown-width: 300px;
}

.dropdownWidth--large {
  --dropdown-width: 350px;
}

.raDropdownHeader {
  padding: 9px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;

  &:hover {
    border-color: #888;
  }
}

.raRotate {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

.raDropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
}

.raSearchContainer {
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.raSearchIcon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.raSearchInput {
  width: 100%;
  padding: 6px 6px 6px 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;

  &:focus {
    border-color: #666;
  }
}

.raVesselGroups {
  overflow-y: auto;
  flex: 1;
  padding: 8px 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 3px;

    &:hover {
      background: #c1c1c1;
    }
  }

  scrollbar-width: thin;
  scrollbar-color: #d8d8d8 #f8f8f8;
}

.raVesselGroup {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.raGroupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  margin-bottom: 4px;

  h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
  }
}

.raVesselList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.raVesselItem {
  padding: 8px 17px 0px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: #f0f0f0;
  }

  label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
  }
}

.raSelectAllContainer {
  bottom: 0;
  padding: 10px;
  border-top: 1px solid #ddd;
  padding-left: 12px;
  color: $color-fleet-blue;
  z-index: 1;
}

.raSelectAlltext {
  cursor: pointer;
  text-decoration: underline !important;
  text-underline-offset: 2px;
  all: unset;
}

.raSelectAlltext:focus:not(:focus-visible) {
  outline: none;
}

@media (max-width: 1430px) {
  .raDropdownContainer {
    width: 100% !important;
  }

  .raDropdownMenu {
    max-height: 250px;
  }

  .raGroupHeader h4 {
    font-size: 0.8rem;
  }

  .raSearchInput {
    font-size: 13px;
  }
}

.raCheckbox {
  accent-color: rgb(63, 143, 180);
  width: 18px;
  height: 18px;
  margin-right: 0.75rem;
  cursor: pointer;
}

.raDisplayTextContainer {
  display: flex;
  align-items: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex-grow: 1;
  min-width: 0;
}

.raPlaceholderText {
  color: #a0a0a0;
}

.raVesselNames {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.raMoreBadge {
  background-color: #f0f4f8;
  color: #3f8fb4;
  border-radius: 12px;
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  position: relative;

  &:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 110%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
    white-space: nowrap;
    z-index: 2000;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    font-weight: 400;
  }
}
