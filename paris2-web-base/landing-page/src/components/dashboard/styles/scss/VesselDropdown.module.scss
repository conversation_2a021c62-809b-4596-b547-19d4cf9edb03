@use "./variables" as *;

.dropdownContainer {
  position: relative;
  font-family: Arial, sans-serif;
  font-size: 15px;
  color: #858383;
}

.dropdownHeader {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;

  &:hover {
    border-color: #888;
  }
}

.rotate {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
}

.searchContainer {
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.searchIcon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.searchInput {
  width: 100%;
  padding: 6px 6px 6px 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;

  &:focus {
    border-color: #666;
  }
}

.vesselGroups {
  overflow-y: auto;
  flex: 1;
  padding: 8px 0;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f8f8f8;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d8d8d8;
    border-radius: 3px;

    &:hover {
      background: #c1c1c1;
    }
  }

  scrollbar-width: thin;
  scrollbar-color: #d8d8d8 #f8f8f8;
}

.vesselGroup {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.groupHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  margin-bottom: 4px;

  h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
  }
}

.vesselList {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: -10px;
}

.vesselItem {
  padding: 1px 17px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: #f0f0f0;
  }

  label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
  }
}

.selectAllContainer {
  position: sticky;
  bottom: 0;
  padding: 10px;
  border-top: 1px solid #ddd;
  padding-left: 12px;
  color: #1F4A70;
  z-index: 1;
}

.selectAlltext {
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.selectAlltext:focus:not(:focus-visible) {
  outline: none;
}

@media (max-width: 600px) {
  .dropdownContainer {
    width: 100% !important;
  }

  .dropdownMenu {
    max-height: 250px;
  }

  .groupHeader h4 {
    font-size: 0.8rem;
  }

  .searchInput {
    font-size: 13px;
  }
}

.checkbox {
  accent-color: rgb(63, 143, 180);
  width: 16px;
  height: 16px;
  margin-right: 0.75rem;
  cursor: pointer;
}
