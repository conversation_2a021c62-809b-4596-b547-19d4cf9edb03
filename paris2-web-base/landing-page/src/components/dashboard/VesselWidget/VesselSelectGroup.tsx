import React from 'react';
import { VesselSelectGroupProps } from '../../../types/types';
import { VesselDropdown } from './VesselDropdown';
import styles from '../styles/scss/VesselSelectGroup.module.scss';

/**
 * A simple wrapper component for the VesselDropdown.
 * Wrapped with React.memo because it's a pure component.
 * It will only re-render if its props change.
 */
export const VesselSelectGroup: React.FC<VesselSelectGroupProps> = React.memo(
  ({
    index,
    config,
    selectedVessels,
    groups,
    onChange,
    isSearchBoxVisible,
    isSelectAllVisible,
  }) => {
    if (!groups) {
      // In a real app, you might render an error boundary or a disabled state.
      console.error('VesselSelectGroup: `groups` prop is missing.');
      return null;
    }

    // Memoize the callback to ensure stable props for VesselDropdown.
    const handleSelectionChange = React.useCallback(
      (newSelected: string[]) => {
        onChange(index, newSelected);
      },
      [index, onChange],
    );

    return (
      <div className={styles.selectWrapper}>
        <VesselDropdown
          groups={groups}
          selectedVessels={selectedVessels}
          onSelectionChange={handleSelectionChange}
          placeholder={config.placeholder}
          width={config.width || '200px'}
          // Pass the new visibility props down
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      </div>
    );
  },
);
