import React from 'react';
import { Search } from 'lucide-react';
import {
  DropdownFooterProps,
  SearchInputProps,
  VesselDropdownMenuProps,
  VesselGroupListProps,
  VesselOption,
} from '../../../types/types';
import styles from '../styles/scss/VesselDropdown.module.scss';

// --- Search Input Sub-component ---

const SearchInput: React.FC<SearchInputProps> = React.memo(({ searchTerm, onSearchChange }) => (
  <div className={styles.raSearchContainer}>
    <Search size={16} className={styles.raSearchIcon} />
    <input
      type="text"
      placeholder="Search"
      value={searchTerm}
      onChange={(e) => onSearchChange(e.target.value)}
      className={styles.raSearchInput}
      aria-label="Search vessels"
    />
  </div>
));

// --- Vessel List Sub-component ---

const VesselGroupList: React.FC<VesselGroupListProps> = ({
  filteredGroups,
  selectedVessels,
  onToggleVessel,
  onToggleGroup
}) => (
  <div className={styles.raVesselGroups}>
    {filteredGroups.map((group, groupIndex) => (
      <div key={`group-${groupIndex}`} className={styles.raVesselGroup}>
        <ul className={styles.raVesselList}>
          {group.vessels.map((vessel, vesselIndex) => (
            <li key={`vessel-${groupIndex}-${vesselIndex}`} className={styles.raVesselItem}>
              <label>
                <input
                  type="checkbox"
                  checked={selectedVessels.includes(vessel.name)}
                  onChange={() => onToggleVessel(vessel.name)}
                  className={styles.raCheckbox}
                />
                {vessel.name}
              </label>
            </li>
          ))}
        </ul>
      </div>
    ))}
  </div>
);

// --- Footer Sub-component ---

const DropdownFooter: React.FC<DropdownFooterProps> = React.memo(
  ({ isAllSelected, onToggleAll }) => (
    <div className={styles.raSelectAllContainer}>
      <button onClick={onToggleAll} className={styles.raSelectAlltext}>
        <span>{`${isAllSelected ? 'Clear All' : 'Select All'}`}</span>
      </button>
    </div>
  ),
);

// --- Main Menu Component (Composer) ---

export const VesselDropdownMenu: React.FC<VesselDropdownMenuProps> = (props) => (
  <div className={styles.raDropdownMenu}>
    {props.isSearchBoxVisible && (
      <SearchInput searchTerm={props.searchTerm} onSearchChange={props.onSearchChange} />
    )}

    <VesselGroupList {...props} />

    {props.isSelectAllVisible && (
      <DropdownFooter isAllSelected={props.isAllSelected} onToggleAll={props.onToggleAll} />
    )}
  </div>
);