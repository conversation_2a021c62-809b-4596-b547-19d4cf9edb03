import React from 'react';
import { Search } from 'lucide-react';
import {
  DropdownFooterProps,
  SearchInputProps,
  VesselDropdownMenuProps,
  VesselGroupListProps,
  VesselGroupOption,
  VesselOption,
} from '../../../types/types';
import styles from '../styles/scss/VesselDropdown.module.scss';

// --- Search Input Sub-component ---

const SearchInput: React.FC<SearchInputProps> = React.memo(({ searchTerm, onSearchChange }) => (
  <div className={styles.searchContainer}>
    <Search size={16} className={styles.searchIcon} />
    <input
      type="text"
      placeholder="Search"
      value={searchTerm}
      onChange={(e) => onSearchChange(e.target.value)}
      className={styles.searchInput}
      aria-label="Search vessels"
    />
  </div>
));

// --- Vessel List Sub-component ---

const VesselGroupList: React.FC<VesselGroupListProps> = React.memo(
  ({ filteredGroups, selectedVessels, onToggleVessel, onToggleGroup }) => (
    <div className={styles.vesselGroups}>
      {filteredGroups.map((group, groupIdx) => (
        <div key={groupIdx} className={styles.vesselGroup}>
          <div className={styles.groupHeader} onClick={() => onToggleGroup(group)}>
            <h4>{group.title}</h4>
          </div>
          <ul className={styles.vesselList}>
            {group.vessels.map((vessel: VesselOption, vesselIdx) => (
              <li key={vesselIdx} className={styles.vesselItem}>
                <label>
                  <input
                    type="checkbox"
                    checked={selectedVessels.includes(vessel.name)}
                    onChange={() => onToggleVessel(vessel.name)}
                    className={styles.checkbox}
                  />
                  {vessel.name}
                </label>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  ),
);

// --- Footer Sub-component ---

const DropdownFooter: React.FC<DropdownFooterProps> = React.memo(
  ({ isAllSelected, onToggleAll }) => (
    <div className={styles.selectAllContainer}>
      <button onClick={onToggleAll} className={styles.selectAlltext}>
        <span>{`${isAllSelected ? 'Clear All' : 'Select All'}`}</span>
      </button>
    </div>
  ),
);

// --- Main Menu Component (Composer) ---

export const VesselDropdownMenu: React.FC<VesselDropdownMenuProps> = (props) => (
  <div className={styles.dropdownMenu}>
    {props.isSearchBoxVisible && (
      <SearchInput searchTerm={props.searchTerm} onSearchChange={props.onSearchChange} />
    )}

    <VesselGroupList {...props} />

    {props.isSelectAllVisible && (
      <DropdownFooter isAllSelected={props.isAllSelected} onToggleAll={props.onToggleAll} />
    )}
  </div>
);
