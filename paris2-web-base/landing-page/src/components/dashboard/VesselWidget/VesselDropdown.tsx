import React, { useState, useMemo, useCallback } from 'react';
import { ChevronDown } from 'lucide-react';
import { VesselDropdownProps, VesselGroupOption, VesselOption } from '../../../types/types';
import { useDropdown } from '../../../hooks/useDropdown';
import { VesselDropdownMenu } from './VesselDropdownMenu';
import styles from '../styles/scss/VesselDropdown.module.scss';

export function VesselDropdown({
  groups = [],
  selectedVessels,
  onSelectionChange,
  placeholder = 'My Vessels',
  width,
  isSearchBoxVisible = true,
  isSelectAllVisible = true,
}: VesselDropdownProps) {
  const { dropdownRef, isOpen, toggleDropdown } = useDropdown();
  const [searchTerm, setSearchTerm] = useState('');

  const { filteredGroups, allVessels, isAllSelected } = useMemo(() => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();

    // The filtering logic is updated to handle objects
    const filtered = groups
      .map((group: VesselGroupOption) => ({
        ...group,
        vessels: group.vessels.filter((vessel: VesselOption) =>
          vessel.name.toLowerCase().includes(lowerCaseSearchTerm),
        ),
      }))
      .filter((group) => group.vessels.length > 0);

    // The logic for getting all vessels and checking if they are selected is also updated
    const all = groups.flatMap((g: VesselGroupOption) => g.vessels.map((v) => v.name));
    const allAreSelected = all.length > 0 && all.every((vName) => selectedVessels.includes(vName));

    return {
      filteredGroups: filtered,
      allVessels: all,
      isAllSelected: allAreSelected,
    };
  }, [groups, searchTerm, selectedVessels]);

  // The toggle logic now works with the vessel's name
  const handleToggleVessel = useCallback(
    (vesselName: string) => {
      const newSelected = selectedVessels.includes(vesselName)
        ? selectedVessels.filter((v) => v !== vesselName)
        : [...selectedVessels, vesselName];
      onSelectionChange(newSelected);
    },
    [selectedVessels, onSelectionChange],
  );

  const handleToggleGroup = useCallback(
    (group: VesselGroupOption) => {
      const groupVesselNames = group.vessels.map((v) => v.name);
      const allInGroupSelected = groupVesselNames.every((vName) => selectedVessels.includes(vName));
      const newSelected = allInGroupSelected
        ? selectedVessels.filter((v) => !groupVesselNames.includes(v))
        : Array.from(new Set([...selectedVessels, ...groupVesselNames]));
      onSelectionChange(newSelected);
    },
    [selectedVessels, onSelectionChange],
  );

  const handleToggleAll = useCallback(() => {
    onSelectionChange(isAllSelected ? [] : allVessels);
  }, [isAllSelected, allVessels, onSelectionChange]);

  const displayText =
    selectedVessels.length > 0 ? `${selectedVessels.length} selected` : placeholder;

  return (
    <div className={styles.dropdownContainer} ref={dropdownRef} style={{ width }}>
      <div
        className={styles.dropdownHeader}
        onClick={toggleDropdown}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <span>{displayText}</span>
        <ChevronDown size={16} className={isOpen ? styles.rotate : ''} />
      </div>

      {isOpen && (
        <VesselDropdownMenu
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filteredGroups={filteredGroups}
          selectedVessels={selectedVessels}
          onToggleVessel={handleToggleVessel}
          onToggleGroup={handleToggleGroup}
          isAllSelected={isAllSelected}
          onToggleAll={handleToggleAll}
          // Pass the new visibility props down
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      )}
    </div>
  );
}
