import React, { useState, useMemo, useCallback } from 'react';
import { ChevronDown } from 'lucide-react';
import classNames from 'classnames';
import { VesselDropdownProps, VesselGroup, VesselOption } from '../../../types/types';
import { useDropdown } from '../../../hooks/useDropdown';
import { VesselDropdownMenu } from './VesselDropdownMenu';
import styles from '../styles/scss/VesselDropdown.module.scss';

export function VesselDropdown({
  groups = [],
  selectedVessels,
  onSelectionChange,
  placeholder = 'My Vessels',
  width,
  isSearchBoxVisible = true,
  isSelectAllVisible = true,
}: Readonly<VesselDropdownProps>) {
  const { dropdownRef, isOpen, toggleDropdown } = useDropdown();
  const [searchTerm, setSearchTerm] = useState('');

  const { filteredGroups, allVessels, isAllSelected } = useMemo(() => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const filtered = groups
      .map((group: VesselGroup) => ({
        ...group,
        vessels: group.vessels.filter((vessel: VesselOption) =>
          vessel.name.toLowerCase().includes(lowerCaseSearchTerm),
        ),
      }))
      .filter((group) => group.vessels.length > 0);

    const all = groups.flatMap((g: VesselGroup) => g.vessels.map((v) => v.name));
    const allAreSelected = all.length > 0 && all.every((vName) => selectedVessels.includes(vName));

    return {
      filteredGroups: filtered,
      allVessels: all,
      isAllSelected: allAreSelected,
    };
  }, [groups, searchTerm, selectedVessels]);

  const handleToggleVessel = useCallback(
    (vesselName: string) => {
      const newSelected = selectedVessels.includes(vesselName)
        ? selectedVessels.filter((v) => v !== vesselName)
        : [...selectedVessels, vesselName];
      onSelectionChange(newSelected);
    },
    [selectedVessels, onSelectionChange],
  );

  const handleToggleGroup = useCallback(
    (group: VesselGroup) => {
      const groupVesselNames = group.vessels.map((v) => v.name);
      const allInGroupSelected = groupVesselNames.every((vName) => selectedVessels.includes(vName));
      const newSelected = allInGroupSelected
        ? selectedVessels.filter((v) => !groupVesselNames.includes(v))
        : Array.from(new Set([...selectedVessels, ...groupVesselNames]));
      onSelectionChange(newSelected);
    },
    [selectedVessels, onSelectionChange],
  );

  const handleToggleAll = useCallback(() => {
    onSelectionChange(isAllSelected ? [] : allVessels);
  }, [isAllSelected, allVessels, onSelectionChange]);

  const displayText = useMemo(() => {
    if (selectedVessels.length === 0) {
      return <span className={styles.raPlaceholderText}>{placeholder}</span>;
    }

    const maxVisible = 2;
    const isOverLimit = selectedVessels.length > maxVisible;

    if (isOverLimit) {
      const visibleVessels = selectedVessels.slice(0, maxVisible).join(', ');
      const moreCount = selectedVessels.length - maxVisible;
      const tooltipContent = selectedVessels.join(', ');

      return (
        <>
          <span className={styles.raVesselNames}>{visibleVessels}</span>
          <span className={styles.raMoreBadge} data-tooltip={tooltipContent}>
            &nbsp; +{moreCount} more
          </span>
        </>
      );
    }

    return <span className={styles.raVesselNames}>{selectedVessels.join(', ')}</span>;
  }, [selectedVessels, placeholder]);

  const getWidthClass = (width: string) => {
    switch (width) {
      case '200px':
        return styles.dropdownWidthSmall;
      case '300px':
        return styles.dropdownWidthMedium;
      case '350px':
        return styles.dropdownWidthLarge;
      default:
        return '';
    }
  };

  return (
    <div className={classNames(styles.raDropdownContainer, getWidthClass(width))} ref={dropdownRef}>
      <div
        className={styles.raDropdownHeader}
        onClick={toggleDropdown}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <div className={styles.raDisplayTextContainer}>{displayText}</div>
        <ChevronDown
          size={16}
          className={classNames(styles.raChevron, { [styles.raRotate]: isOpen })}
        />
      </div>

      {isOpen && (
        <VesselDropdownMenu
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filteredGroups={filteredGroups}
          selectedVessels={selectedVessels}
          onToggleVessel={handleToggleVessel}
          onToggleGroup={handleToggleGroup}
          isAllSelected={isAllSelected}
          onToggleAll={handleToggleAll}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      )}
    </div>
  );
}
