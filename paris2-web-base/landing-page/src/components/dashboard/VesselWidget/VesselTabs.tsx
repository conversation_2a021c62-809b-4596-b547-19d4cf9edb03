import React from "react";

interface VesselTabsProps {
  tabs: string[];
  activeTab: string;
  IsAllTabVisible?: boolean;
  onTabChange: (tab: string) => void;
}

/**
 * A component focused only on rendering and managing tab navigation.
 */
export const VesselTabs: React.FC<VesselTabsProps> = ({
  tabs,
  activeTab,
  IsAllTabVisible,
  onTabChange,
}) => {
  if (tabs.length === 0) {
    return null; // Don't render anything if there are no tabs
  }

  const displayTabs = IsAllTabVisible ? ["All", ...tabs] : tabs;

  return (
    <div className="tabs-container">
      {displayTabs.map((tab) => (
        <button
          key={tab}
          onClick={() => onTabChange(tab)}
          className={`tab-button ${activeTab === tab ? "active" : ""}`}
        >
          {tab}
          {activeTab === tab && <span className="active-tab-indicator"></span>}
        </button>
      ))}
    </div>
  );
};
