import React, { useState, useMemo, useCallback } from 'react';
import { RotateCw } from 'lucide-react';
import { VesselModuleProps, Vessel } from '../../../types/types';
import VesselTable from './VesselTable';
import VesselGrid from './VesselGrid';
import { VesselModuleHeader } from './VesselModuleHeader';
import { VesselTabs } from './VesselTabs';
import { VesselSelectors } from './VesselSelectors';
import { ModuleModal } from './ModuleModal';
import '../styles/scss/VesselModule.scss';

// Define a type for our sorting configuration
type SortConfig = {
  key: string;
  direction: 'ascending' | 'descending';
} | null;

// const defaultContainerSize = { width: '696px', height: '490px' };
// const defaultModalSize = { width: '700px', height: '700px' };

export default function VesselModule({
  title,
  vessels,
  tabs,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  IsVesselSelectVisible,
  IsAllTabVisible,
  multiVesselSelects = [],
  vesselSelectPosition = 'before',
  containerSize,
  modalSize,
  tableHeaders,
  badgeColors,
  onRefresh,
  gridComponent,
  defaultComponent = 'list',
  cellStyleType,
  ...displayProps
}: VesselModuleProps) {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>(defaultComponent);
  const [activeTab, setActiveTab] = useState<string>(() =>
    IsAllTabVisible ? 'All' : tabs[0] || 'All',
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => []),
  );
  
  // Create a style object for the CSS variables.
  // Use a fallback to an empty object if props are not provided.
  const containerStyle = {
    '--container-width': containerSize?.width,
    '--container-height': containerSize?.height,
  };

  // NEW: State to hold the current sorting configuration
  const [sortConfig, setSortConfig] = useState<SortConfig>(null);

  // The main logic for filtering and sorting the data
  const processedVessels = useMemo(() => {
    let filteredData = vessels || [];

    // 1. Filtering logic 
    filteredData =
      activeTab === 'All' ? filteredData : filteredData.filter((v: Vessel) => v.type === activeTab);

    const vesselNameSelections = selectStates[0];
    if (vesselNameSelections && vesselNameSelections.length > 0) {
      const allVesselOptions = multiVesselSelects[0]?.groups.flatMap((g) => g.vessels) || [];
      const selectedVesselIdentifiers = new Set(
        allVesselOptions
          .filter((opt) => vesselNameSelections.includes(opt.name))
          .map((opt) => `${opt.vessel_id}-${opt.vessel_ownership_id}`),
      );
      if (selectedVesselIdentifiers.size > 0) {
        filteredData = filteredData.filter((vessel) =>
          selectedVesselIdentifiers.has(`${vessel.vessel_id}-${vessel.vessel_ownership_id}`),
        );
      }
    }

    const levelRaSelections = selectStates[1];
    if (levelRaSelections && levelRaSelections.length > 0) {
      filteredData = filteredData.filter((vessel: Vessel) => {
        const vesselLevelOfRa = vessel.vesselData[1];
        return levelRaSelections.includes(vesselLevelOfRa as string);
      });
    }

    // 2. NEW: Sorting logic
    if (sortConfig !== null) {
      // Get the index of the header to determine which data field to use
      const headerIndex = tableHeaders.indexOf(sortConfig.key);
      if (headerIndex !== -1) {
        // Create a mutable copy before sorting
        filteredData = [...filteredData].sort((a, b) => {
          let aValue, bValue;

          // The first header corresponds to the top-level 'name' property
          if (headerIndex === 0) {
            aValue = a.name;
            bValue = b.name;
          } else {
            // Other headers correspond to the 'vesselData' array (index is offset by 1)
            aValue = a.vesselData[headerIndex - 1];
            bValue = b.vesselData[headerIndex - 1];
          }

          if (aValue < bValue) {
            return sortConfig.direction === 'ascending' ? -1 : 1;
          }
          if (aValue > bValue) {
            return sortConfig.direction === 'ascending' ? 1 : -1;
          }
          return 0;
        });
      }
    }

    return filteredData;
  }, [vessels, activeTab, selectStates, multiVesselSelects, sortConfig, tableHeaders]);

  const handleRefresh = useCallback(() => {
    onRefresh();
    setLastUpdated(new Date());
  }, [onRefresh]);

  const handleSelectChange = useCallback((index: number, newSelected: string[]) => {
    setSelectStates((prevStates) => {
      const newStates = [...prevStates];
      newStates[index] = newSelected;
      return newStates;
    });
  }, []);

  // NEW: Handler to update the sort configuration
  const handleSort = useCallback((key: string) => {
    setSortConfig(currentSortConfig => {
      let direction: 'ascending' | 'descending' = 'ascending';
      // If clicking the same column, toggle the direction
      if (currentSortConfig && currentSortConfig.key === key && currentSortConfig.direction === 'ascending') {
        direction = 'descending';
      }
      return { key, direction };
    });
  }, []);


  const renderViewContent = (isModal: boolean) =>
    viewMode === 'list' ? (
      <VesselTable
        vessels={processedVessels} // Use the new sorted and filtered data
        tableHeaders={tableHeaders}
        badgeColors={badgeColors}
        cellStyleType={cellStyleType}
        sortConfig={sortConfig} // Pass down sort state
        onSort={handleSort} // Pass down sort handler
        {...displayProps}
      />
    ) : (
      <VesselGrid
        vessels={processedVessels} // Also use processed data for the grid
        tableHeaders={tableHeaders}
        badgeColors={badgeColors}
        isModal={isModal}
        gridComponent={gridComponent}
        {...displayProps}
      />
    );

  const renderModuleCore = (isModal: boolean) => (
    <>
      <VesselModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModal}
        IsiconRenderVisible={IsiconRenderVisible}
        IsenLargeIconVisible={IsenLargeIconVisible}
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />

      <div className="last-updated-container">
        <p className="last-updated-text">
          Last Updated:{' '}
          {lastUpdated.toLocaleString(undefined, {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })}
          <RotateCw onClick={handleRefresh} className="refresh-icon" />
        </p>
      </div>

      {IsVesselSelectVisible && vesselSelectPosition === 'before' && (
        <VesselSelectors
          multiVesselSelects={multiVesselSelects}
          selectStates={selectStates}
          onSelectChange={handleSelectChange}
        />
      )}

      <VesselTabs
        tabs={tabs}
        activeTab={activeTab}
        IsAllTabVisible={IsAllTabVisible}
        onTabChange={setActiveTab}
      />

      {IsVesselSelectVisible && vesselSelectPosition === 'after' && (
        <VesselSelectors
          multiVesselSelects={multiVesselSelects}
          selectStates={selectStates}
          onSelectChange={handleSelectChange}
        />
      )}

      <div className={`content-container`}>{renderViewContent(isModal)}</div>
    </>
  );

  return (
    <>
      <div className="vessel-module-container" style={containerStyle}>
        {renderModuleCore(false)}
      </div>
      <ModuleModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} size={modalSize}>
        {renderModuleCore(true)}
      </ModuleModal>
    </>
  );
}
