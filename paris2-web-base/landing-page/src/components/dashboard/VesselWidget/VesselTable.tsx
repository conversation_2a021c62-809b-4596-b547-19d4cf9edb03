import React from 'react';
import { MailI<PERSON>, ArrowUp, ArrowDown, ChevronsUpDown } from 'lucide-react';
import classNames from 'classnames';
import { Vessel } from '../../../types/types';
import { useInfiniteScroll } from '../../../hooks/useInfiniteScroll';
import Spinner from './Spinner';
import styles from '../styles/scss/VesselTable.module.scss';

type SortConfig = {
  key: string;
  direction: 'ascending' | 'descending';
} | null;

interface VesselTableProps {
  vessels: Vessel[];
  tableHeaders: string[];
  badgeColors: string[];
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  isFetchingNextPage?: boolean;
  isLoading?: boolean;
  fetchNextPage?: () => void;
  pagination?: {
    page: number;
    totalPages: number;
  };
  cellStyleType?: 'default' | 'conditional';
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}

// --- SUB-COMPONENTS ---

const ExternalLinkIcon: React.FC<{ size?: number }> = ({ size = 20 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
    <polyline points="15 3 21 3 21 9"></polyline>
    <line x1="10" y1="14" x2="21" y2="3"></line>
  </svg>
);

function hexToRgb(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : '128, 128, 128';
}

const DataCellContent: React.FC<{
  cellStyleType: 'default' | 'conditional';
  columnIndex: number;
  value: string | number;
  badgeColors: string[];
}> = React.memo(({ cellStyleType, columnIndex, value, badgeColors }) => {
  if (cellStyleType === 'conditional') {
    // Corresponds to the first item in `vesselData`
    if (columnIndex === 0) {
      const str = value.toString();
      const truncated = str.length > 15 ? `${str.slice(0, 15)}...` : str;
      return <span className={styles.vesselSecondColumnEntry} title={str}>{truncated}</span>;
    }
    
    // Corresponds to the second item in `vesselData`
    if (columnIndex === 1) {
      let selectedColor: string;
      switch (value) {
        case "Critical": selectedColor = badgeColors[0] || "#f44336"; break;
        case "Special": selectedColor = badgeColors[1] || "#fbc02d"; break;
        case "Unassigned": selectedColor = badgeColors[2] || "#808080"; break;
        default: selectedColor = "#808080"; break;
      }
      const cellStyle = { '--status-rgb': hexToRgb(selectedColor) } as React.CSSProperties;
      return <span className={styles.statusBadge} style={cellStyle}>{value.toString()}</span>;
    }

    // Corresponds to the third item in `vesselData`
    if (columnIndex === 2) {
      const badgeClasses = classNames(styles.statusBadge, {
        [styles.approvedBadge]: value === "Approved",
        [styles.approvedWithConditionBadge]: value === "Approved with Condition",
        [styles.rejectedBadge]: value === "Rejected",
      });

      if (value === "Pending" || !["Approved", "Approved with Condition", "Rejected"].includes(value as string)) {
        const color = value === "Pending" ? (badgeColors[1] || "#fbc02d") : "#808080";
        const cellStyle = { '--status-rgb': hexToRgb(color) } as React.CSSProperties;
        return <span className={badgeClasses} style={cellStyle}>{value.toString()}</span>;
      }
      return <span className={badgeClasses}>{value.toString()}</span>;
    }
  }

  const defaultBadgeColor = badgeColors[columnIndex] || "#808080";
  const badgeStyle = {
    '--badge-bg-color': defaultBadgeColor,
    '--badge-text-color': ["#fbc02d", "#ffeb3b"].includes(defaultBadgeColor.toLowerCase()) ? "black" : "white",
  } as React.CSSProperties;

  return <span className={styles.badge} style={badgeStyle}>{value.toString()}</span>;
});

const VesselTableHeader: React.FC<{
  headers: string[];
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}> = React.memo(({ headers, sortConfig, onSort }) => {
  const getSortIcon = (headerKey: string) => {
    if (!sortConfig || sortConfig.key !== headerKey) {
      return <ChevronsUpDown size={14} className={classNames(styles.sortIcon, styles.neutral)} />;
    }
    return sortConfig.direction === 'ascending' 
      ? <ArrowUp size={14} className={classNames(styles.sortIcon, styles.active)} />
      : <ArrowDown size={14} className={classNames(styles.sortIcon, styles.active)} />;
  };

  return (
    <thead className={styles.tableHeader}>
      <tr>
        {headers.map((header, idx) => (
          <th key={idx} className={classNames({ [styles.textLeft]: idx === 0 })} onClick={() => onSort(header)}>
            <div className={styles.headerContent}>
              {header}
              {getSortIcon(header)}
            </div>
          </th>
        ))}
        <th className={classNames(styles.actionHeader, styles.nonSortable)}>
          <div className={styles.headerContent}>Actions</div>
        </th>
      </tr>
    </thead>
  );
});

// --- Sub-component: Table Row (FIXED) ---
const VesselTableRow: React.FC<{
  vessel: Vessel;
  badgeColors: string[];
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  cellStyleType: 'default' | 'conditional';
}> = React.memo(({ vessel, badgeColors, onSendEmail, onVesselClick, cellStyleType }) => (
    <tr className={styles.tableRow}>
      {/* Column 1: Vessel Name is rendered separately first */}
      <td className={styles.vesselNameCell}>
        <button onClick={() => onVesselClick(vessel)} className={styles.vesselNameButton}>
          {vessel.name}
        </button>
      </td>
      
      {/* Subsequent columns are mapped from the vesselData array */}
      {vessel.vesselData.map((value, i) => (
        <td key={i}>
          {/* The index `i` correctly corresponds to the columnIndex for DataCellContent */}
          <DataCellContent cellStyleType={cellStyleType} columnIndex={i} value={value} badgeColors={badgeColors} />
        </td>
      ))}

      {/* Final Column: Actions */}
      <td className={styles.emailCell}>
        <div className={styles.emailButtonWrapper}>
          <button onClick={() => onSendEmail(vessel)} className={styles.emailButton}>
            {cellStyleType === 'conditional' ? <ExternalLinkIcon /> : <MailIcon size={20} />}
          </button>
          <div className={styles.tooltip}>
            {cellStyleType === 'conditional' ? "View Details" : "Send Email"}
          </div>
        </div>
      </td>
    </tr>
));

// --- Main Component ---
export default function VesselTable({
  vessels,
  tableHeaders,
  badgeColors,
  onSendEmail,
  onVesselClick,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  cellStyleType = 'default',
  sortConfig,
  onSort,
}: VesselTableProps) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;
  const dataHeaders = tableHeaders.filter(h => h.toLowerCase() !== 'action');
  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderBody = () => {
    if (isLoading) {
      return (
        <tr>
          <td colSpan={dataHeaders.length + 1} className={styles.statusCell}>
            <Spinner />
          </td>
        </tr>
      );
    }
    if (vessels.length === 0) {
      return (
        <tr>
          <td colSpan={dataHeaders.length + 1} className={styles.statusCell}>
            No results found
          </td>
        </tr>
      );
    }
    return vessels.map((vessel, idx) => (
      <VesselTableRow
        key={`${vessel.vessel_id}-${idx}`}
        vessel={vessel}
        badgeColors={badgeColors}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        cellStyleType={cellStyleType}
      />
    ));
  };

  return (
    <div ref={containerRef} onScroll={handleScroll} className={styles.tableContainer}>
      <table className={styles.table}>
        <VesselTableHeader headers={dataHeaders} sortConfig={sortConfig} onSort={onSort} />
        <tbody>{renderBody()}</tbody>
      </table>
      {isFetchingNextPage && (
        <div className={styles.spinnerContainer}>
          <Spinner />
        </div>
      )}
    </div>
  );
}
