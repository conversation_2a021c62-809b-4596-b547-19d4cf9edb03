import React from "react";
import { LayoutGrid, List, Maximize2, Minimize2 } from "lucide-react";
import enlargeImage from "../../../assets/images/enlarge.jpeg"; 

interface VesselModuleHeaderProps {
  title: string;
  viewMode: "list" | "grid";
  isModal: boolean;
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  onViewModeChange: (mode: "list" | "grid") => void;
  onToggleModal: () => void;
}


export const VesselModuleHeader: React.FC<VesselModuleHeaderProps> = ({
  title,
  viewMode,
  isModal,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  onViewModeChange,
  onToggleModal,
}) => {
  return (
    <div className="vessel-module-header">
      <h2 className="vessel-module-title">{title}</h2>
      <div className="vessel-module-controls">
        {IsiconRenderVisible && (
          <div className="view-toggle-container">
            <button
              onClick={() => onViewModeChange("grid")}
              className={`view-toggle-button ${viewMode === "grid" ? "active" : ""}`}
              aria-label="Grid view"
            >
              <LayoutGrid className="view-toggle-icon" />
            </button>
            <button
              onClick={() => onViewModeChange("list")}
              className={`view-toggle-button ${viewMode === "list" ? "active" : ""}`}
              aria-label="List view"
            >
              <List className="view-toggle-icon" />
            </button>
          </div>
        )}

        {IsenLargeIconVisible &&
          (isModal ? (
            <Minimize2
              className="enlarge-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ) : (
            // <img
            //   src={enlargeImage}
            //   alt="Enlarge"
            //   className="enlarge-icon"
            //   onClick={onToggleModal}
            //   aria-label="Enlarge view"
            // />
            <Maximize2
              className="enlarge-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ))}
      </div>
    </div>
  );
};
