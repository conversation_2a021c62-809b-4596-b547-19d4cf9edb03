import React from 'react';
import { LayoutGrid, List, Minimize2 } from 'lucide-react';
import classNames from 'classnames';

interface VesselModuleHeaderProps {
  title: string;
  viewMode: 'list' | 'grid';
  isModal: boolean;
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  onViewModeChange: (mode: 'list' | 'grid') => void;
  onToggleModal: () => void;
}

export const VesselModuleHeader: React.FC<VesselModuleHeaderProps> = ({
  title,
  viewMode,
  isModal,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  onViewModeChange,
  onToggleModal,
}) => {
  return (
    <div className="ra-vessel-module-header">
      <h2 className="ra-vessel-module-title">{title}</h2>
      <div className="ra-vessel-module-controls">
        {IsiconRenderVisible && (
          <div className="ra-view-toggle-container">
            <button
              onClick={() => onViewModeChange('grid')}
              className={classNames('ra-view-toggle-button', {
                active: viewMode === 'grid',
              })}
              aria-label="Grid view"
            >
              <LayoutGrid className="ra-view-toggle-icon" />
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={classNames('ra-view-toggle-button', {
                active: viewMode === 'list',
              })}
              aria-label="List view"
            >
              <List className="ra-view-toggle-icon" />
            </button>
          </div>
        )}

        {IsenLargeIconVisible &&
          (isModal ? (
            <Minimize2
              className="ra-enlarge-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ) : (
            <svg
              width="20"
              height="21"
              viewBox="0 0 20 21"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="ra-enlarge-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            >
              <path
                d="M17.6929 3.94936V8.23187H19V1.7327H12.5008V3.03977H16.7833L2.30707 17.516V13.2335H1V19.7327H7.49918V18.4256H3.21667L17.6929 3.94936Z"
                fill="#1F4A70"
              />
            </svg>
          ))}
      </div>
    </div>
  );
};