import React from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-3d";
import { useHighchartsDonut } from "../../../hooks/useHighchartsDonut"; 

interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface DonutChartProps {
  data: ChartDataItem[];
}

/**
 * A presentational component for displaying a 3D donut chart.
 * All complex configuration logic is handled by the `useHighchartsDonut` hook,
 * making this component clean and focused on rendering.
 */
const Dynamic3DDonutChart: React.FC<DonutChartProps> = ({ data }) => {

  const { options, tooltipStyle } = useHighchartsDonut({ data });

  return (
    <div style={{ width: "100%", maxWidth: "600px", margin: "auto" }}>
      <style>{tooltipStyle}</style>
      <HighchartsReact highcharts={Highcharts} options={options} />
    </div>
  );
};

export default Dynamic3DDonutChart;
