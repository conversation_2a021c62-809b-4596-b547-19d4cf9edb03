import React from "react";

interface ModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  size: {
    width: string;
    height: string;
  };
  children: React.ReactNode;
}

/**
 * A generic, reusable modal component.
 */
export const ModuleModal: React.FC<ModuleModalProps> = ({
  isOpen,
  onClose,
  size,
  children,
}) => {
  if (!isOpen) {
    return null;
  }

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Close the modal only if the click is on the overlay itself, not its children
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const modalStyle = {
    '--modal-width': size?.width,
    '--modal-height': size?.height,
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content" style={modalStyle}>
        {children}
      </div>
    </div>
  );
};
