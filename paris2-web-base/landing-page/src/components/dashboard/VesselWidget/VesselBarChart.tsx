import React, { useState } from "react";
import { useD3Chart } from "../../../hooks/useD3Chart";

import {
  ScrollableChartElements,
  StickyXAxis,
  ChartTooltip,
  ChartLegend,
} from "./ChartComponents";
import "../styles/scss/VesselBarChart.scss";

interface VesselBarChartProps {
  vessels: any[];
  width?: number;
  heightPerBar?: number;
  valueHeaders: string[];
  badgeColors: string[];
  valueDomain: [number, number];
  isModal?: boolean;
}

const MARGIN = { top: 20, right: 20, bottom: 50, left: 150 };

export default function VesselBarChart({
  vessels,
  width = 700,
  heightPerBar = 40,
  valueHeaders,
  badgeColors,
  valueDomain = [0, 250],
  isModal,
}: VesselBarChartProps) {

  const chartWidth = isModal ? 1000 : width;

  const [tooltip, setTooltip] = useState({
    visible: false,
    content: "",
    x: 0,
    y: 0,
  });

  const {
    xScale,
    yScale,
    barColorScale,
    textColorScale,
    stackedBarData,
    chartHeight,
    totalHeight,
  } = useD3Chart({
    vessels,
    valueHeaders,
    badgeColors,
    valueDomain,
    chartWidth,
    heightPerBar,
    margin: MARGIN,
  });


  const handleMouseOver = (event: React.MouseEvent, segment: any) => {
    const content = `
      <div class="tooltip-content">
        <div class="tooltip-title"><b>${segment.key}</b></div>
        <div class="tooltip-row">
          <span class="tooltip-color" style="background:${barColorScale(
            segment.key,
          )}"></span>
          ${segment.value}
        </div>
      </div>`;
    setTooltip({
      visible: true,
      content,
      x: event.clientX + 20,
      y: event.clientY,
    });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (tooltip.visible) {
      setTooltip((prev) => ({
        ...prev,
        x: event.clientX + 20,
        y: event.clientY,
      }));
    }
  };

  const handleMouseOut = () => {
    setTooltip((prev) => ({ ...prev, visible: false }));
  };

  const handleClick = (vesselName: string, statusKey: string) => {
    const vesselSlug = vesselName.toLowerCase().replace(/\s+/g, "-");
    const statusSlug = statusKey.toLowerCase().replace(/\s+/g, "-");
    // navigate(`/vessel/${vesselSlug}/${statusSlug}`);
  };

  return (
    <div className="vessel-bar-chart-container">
      <div className="chart-scrollable">
        <svg width={chartWidth} height={totalHeight} className="main-chart">
          <g>
            <ScrollableChartElements
              yScale={yScale}
              xScale={xScale}
              height={chartHeight}
              margin={MARGIN}
            />
            {/* Render the bars */}
            {stackedBarData.map((bar) => (
              <g
                key={bar.vesselName}
                transform={`translate(${MARGIN.left}, 0)`}
              >
                {bar.segments.map((segment) => (
                  <g
                    key={segment.key}
                    className="bar-subgroup"
                    style={{ cursor: "pointer" }}
                    onClick={() => handleClick(segment.vesselName, segment.key)}
                    onMouseOver={(e) => handleMouseOver(e, segment)}
                    onMouseMove={handleMouseMove}
                    onMouseOut={handleMouseOut}
                  >
                    <rect
                      x={segment.x}
                      y={segment.y}
                      width={segment.width}
                      height={segment.height}
                      fill={barColorScale(segment.key)}
                    />
                    <text
                      x={segment.x + segment.width / 2}
                      y={segment.y + segment.height / 2}
                      dy=".35em"
                      textAnchor="middle"
                      fill={textColorScale(segment.key)}
                      style={{ fontSize: "12px", pointerEvents: "none" }}
                    >
                      {segment.value}
                    </text>
                  </g>
                ))}
              </g>
            ))}
          </g>
        </svg>
      </div>

      <div className="xAxix-legend-parent ">
        {/* Use the new StickyXAxis component */}
        <StickyXAxis
          xScale={xScale}
          width={chartWidth}
          height={MARGIN.bottom}
          margin={{ left: MARGIN.left }}
        />
        <ChartLegend
          valueHeaders={valueHeaders}
          badgeColors={badgeColors}
          style={{ paddingLeft: isModal ? "340px" : "220px" }}
        />
      </div>

      <ChartTooltip
        content={tooltip.content}
        position={{ x: tooltip.x, y: tooltip.y }}
        isVisible={tooltip.visible}
      />
    </div>
  );
}
