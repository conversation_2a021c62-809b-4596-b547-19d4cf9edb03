import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { VesselDropdown } from '../VesselDropdown';
import { VesselGroup } from '../../../../types/types';

// Mock the useDropdown hook
const mockUseDropdown = jest.fn(() => ({
  dropdownRef: { current: null },
  isOpen: false,
  toggleDropdown: jest.fn(),
}));

jest.mock('../../../../hooks/useDropdown', () => ({
  useDropdown: mockUseDropdown,
}));

// Mock the VesselDropdownMenu component
jest.mock('../VesselDropdownMenu', () => ({
  VesselDropdownMenu: ({ searchTerm, onSearchChange, onToggleVessel }: any) => (
    <div data-testid="vessel-dropdown-menu">
      <input
        data-testid="mock-search"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
      />
      <button onClick={() => onToggleVessel('Test Vessel')}>
        Toggle Test Vessel
      </button>
    </div>
  ),
}));

describe('VesselDropdown Component', () => {
  const mockGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel Alpha' },
        { vessel_id: 2, name: 'Vessel Beta' },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Ship Gamma' },
      ],
    },
  ];

  const defaultProps = {
    groups: mockGroups,
    selectedVessels: [],
    onSelectionChange: jest.fn(),
    placeholder: 'Select vessels',
    width: '300px',
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default placeholder when no vessels selected', () => {
    render(<VesselDropdown {...defaultProps} />);
    
    expect(screen.getByText('Select vessels')).toBeInTheDocument();
  });

  it('renders default placeholder when no custom placeholder provided', () => {
    const { placeholder, ...propsWithoutPlaceholder } = defaultProps;
    render(<VesselDropdown {...propsWithoutPlaceholder} />);
    
    expect(screen.getByText('My Vessels')).toBeInTheDocument();
  });

  it('displays selected vessel names when vessels are selected', () => {
    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={['Vessel Alpha']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
  });

  it('displays multiple selected vessels correctly', () => {
    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={['Vessel Alpha', 'Vessel Beta']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha, Vessel Beta')).toBeInTheDocument();
  });

  it('shows truncated display with "more" badge when more than 2 vessels selected', () => {
    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={['Vessel Alpha', 'Vessel Beta', 'Ship Gamma']}
      />
    );
    
    expect(screen.getByText('Vessel Alpha, Vessel Beta')).toBeInTheDocument();
    expect(screen.getByText('+1 more')).toBeInTheDocument();
  });

  it('calls onSelectionChange when vessel is toggled', () => {
    const onSelectionChangeMock = jest.fn();

    // Mock useDropdown to return isOpen: true
    mockUseDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        onSelectionChange={onSelectionChangeMock}
      />
    );

    const toggleButton = screen.getByText('Toggle Test Vessel');
    fireEvent.click(toggleButton);

    expect(onSelectionChangeMock).toHaveBeenCalledWith(['Test Vessel']);
  });

  it('adds vessel to selection when not already selected', () => {
    const onSelectionChangeMock = jest.fn();

    mockUseDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={[]}
        onSelectionChange={onSelectionChangeMock}
      />
    );

    const toggleButton = screen.getByText('Toggle Test Vessel');
    fireEvent.click(toggleButton);

    expect(onSelectionChangeMock).toHaveBeenCalledWith(['Test Vessel']);
  });

  it('removes vessel from selection when already selected', () => {
    const onSelectionChangeMock = jest.fn();

    mockUseDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(
      <VesselDropdown
        {...defaultProps}
        selectedVessels={['Test Vessel']}
        onSelectionChange={onSelectionChangeMock}
      />
    );

    const toggleButton = screen.getByText('Toggle Test Vessel');
    fireEvent.click(toggleButton);

    expect(onSelectionChangeMock).toHaveBeenCalledWith([]);
  });

  it('applies correct width class for different widths', () => {
    const { container } = render(
      <VesselDropdown {...defaultProps} width="200px" />
    );

    const dropdown = container.querySelector('[class*="dropdownWidth"]');
    expect(dropdown).toBeInTheDocument();
  });

  it('applies medium width class for 300px', () => {
    const { container } = render(
      <VesselDropdown {...defaultProps} width="300px" />
    );

    const dropdown = container.querySelector('[class*="dropdownWidth"]');
    expect(dropdown).toBeInTheDocument();
  });

  it('applies large width class for 350px', () => {
    const { container } = render(
      <VesselDropdown {...defaultProps} width="350px" />
    );

    const dropdown = container.querySelector('[class*="dropdownWidth"]');
    expect(dropdown).toBeInTheDocument();
  });

  it('handles empty groups array', () => {
    render(<VesselDropdown {...defaultProps} groups={[]} />);
    
    expect(screen.getByText('Select vessels')).toBeInTheDocument();
  });

  it('filters vessels based on search term', () => {
    mockUseDropdown.mockReturnValue({
      dropdownRef: { current: null },
      isOpen: true,
      toggleDropdown: jest.fn(),
    });

    render(<VesselDropdown {...defaultProps} />);

    const searchInput = screen.getByTestId('mock-search');
    fireEvent.change(searchInput, { target: { value: 'alpha' } });

    expect(searchInput).toHaveValue('alpha');
  });

  it('has correct accessibility attributes', () => {
    render(<VesselDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole('generic', { hidden: true });
    expect(dropdownHeader).toHaveAttribute('aria-haspopup', 'listbox');
    expect(dropdownHeader).toHaveAttribute('aria-expanded', 'false');
  });
});
