import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import VesselModule from '../VesselModule';
import { Vessel, MultiVesselSelectConfig } from '../../../../types/types';

// Mock all child components
jest.mock('../VesselTable', () => {
  return function MockVesselTable({ vessels, onSort, sortConfig }: any) {
    return (
      <div data-testid="vessel-table">
        <div>Mock Vessel Table</div>
        <div>Vessels: {vessels.length}</div>
        <button onClick={() => onSort('name')}>Sort by Name</button>
      </div>
    );
  };
});

jest.mock('../VesselGrid', () => {
  return function MockVesselGrid({ vessels }: any) {
    return (
      <div data-testid="vessel-grid">
        <div>Mock Vessel Grid</div>
        <div>Vessels: {vessels.length}</div>
      </div>
    );
  };
});

jest.mock('../VesselModuleHeader', () => ({
  VesselModuleHeader: ({ title, viewMode, onViewModeChange, onToggleModal }: any) => (
    <div data-testid="vessel-module-header">
      <h2>{title}</h2>
      <button onClick={() => onViewModeChange('grid')}>Grid View</button>
      <button onClick={() => onViewModeChange('list')}>List View</button>
      <button onClick={onToggleModal}>Toggle Modal</button>
      <div>Current view: {viewMode}</div>
    </div>
  ),
}));

jest.mock('../VesselSelectors', () => ({
  VesselSelectors: ({ multiVesselSelects, onSelectChange }: any) => (
    <div data-testid="vessel-selectors">
      <div>Vessel Selectors: {multiVesselSelects.length}</div>
      <button onClick={() => onSelectChange(0, ['test'])}>Change Selection</button>
    </div>
  ),
}));

jest.mock('../ModuleModal', () => ({
  ModuleModal: ({ isOpen, onClose, children }: any) => (
    isOpen ? (
      <div data-testid="module-modal">
        <button onClick={onClose}>Close Modal</button>
        {children}
      </div>
    ) : null
  ),
}));

describe('VesselModule Component', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: ['data1'],
      type: 'cargo',
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: ['data2'],
      type: 'tanker',
      vessel_id: 2,
    },
  ];

  const mockMultiVesselSelects: MultiVesselSelectConfig[] = [
    {
      placeholder: 'All Vessels',
      width: '300px',
      groups: [],
      isSearchBoxVisible: true,
      isSelectAllVisible: true,
    },
  ];

  const defaultProps = {
    title: 'Test Vessel Module',
    vessels: mockVessels,
    tabs: ['All', 'Active'],
    tableHeaders: ['Name', 'Type', 'Status'],
    badgeColors: ['#ff0000', '#00ff00'],
    multiVesselSelects: mockMultiVesselSelects,
    onRefresh: jest.fn(),
    onSendEmail: jest.fn(),
    onVesselClick: jest.fn(),
    fetchNextPage: jest.fn(),
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    IsVesselSelectVisible: true,
    IsAllTabVisible: true,
    isFetchingNextPage: false,
    isLoading: false,
    pagination: {
      totalItems: 10,
      totalPages: 2,
      page: 1,
      pageSize: 5,
    },
    vesselSelectPosition: 'before' as const,
    containerSize: { width: '100%', height: '500px' },
    modalSize: { width: '80%', height: '80%' },
    defaultComponent: 'list' as const,
    cellStyleType: 'default' as const,
    gridComponent: 'bar' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with correct title', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByText('Test Vessel Module')).toBeInTheDocument();
  });

  it('renders vessel table by default', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
    expect(screen.getByText('Mock Vessel Table')).toBeInTheDocument();
  });

  it('switches to grid view when grid button is clicked', () => {
    render(<VesselModule {...defaultProps} />);
    
    const gridButton = screen.getByText('Grid View');
    fireEvent.click(gridButton);
    
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
    expect(screen.getByText('Mock Vessel Grid')).toBeInTheDocument();
  });

  it('switches back to list view when list button is clicked', () => {
    render(<VesselModule {...defaultProps} defaultComponent="grid" />);
    
    const listButton = screen.getByText('List View');
    fireEvent.click(listButton);
    
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
  });

  it('opens modal when toggle modal button is clicked', () => {
    render(<VesselModule {...defaultProps} />);
    
    const toggleButton = screen.getByText('Toggle Modal');
    fireEvent.click(toggleButton);
    
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
  });

  it('closes modal when close button is clicked', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Open modal first
    const toggleButton = screen.getByText('Toggle Modal');
    fireEvent.click(toggleButton);
    
    // Close modal
    const closeButton = screen.getByText('Close Modal');
    fireEvent.click(closeButton);
    
    expect(screen.queryByTestId('module-modal')).not.toBeInTheDocument();
  });

  it('calls onRefresh when refresh icon is clicked', () => {
    const onRefreshMock = jest.fn();
    render(<VesselModule {...defaultProps} onRefresh={onRefreshMock} />);

    const refreshIcon = screen.getByText(/Last Updated:/).querySelector('.ra-refresh-icon');
    fireEvent.click(refreshIcon!);

    expect(onRefreshMock).toHaveBeenCalledTimes(1);
  });

  it('renders vessel selectors when IsVesselSelectVisible is true', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={true} />);
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('does not render vessel selectors when IsVesselSelectVisible is false', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={false} />);
    
    expect(screen.queryByTestId('vessel-selectors')).not.toBeInTheDocument();
  });

  it('handles vessel selection change', () => {
    render(<VesselModule {...defaultProps} IsVesselSelectVisible={true} />);
    
    const changeButton = screen.getByText('Change Selection');
    fireEvent.click(changeButton);
    
    // Should not throw error
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('handles sorting', () => {
    render(<VesselModule {...defaultProps} />);
    
    const sortButton = screen.getByText('Sort by Name');
    fireEvent.click(sortButton);
    
    // Should not throw error
    expect(screen.getByTestId('vessel-table')).toBeInTheDocument();
  });

  it('displays last updated time', () => {
    render(<VesselModule {...defaultProps} />);
    
    expect(screen.getByText(/Last Updated:/)).toBeInTheDocument();
  });

  it('conditionally sets icon visibility based on vessel data', () => {
    render(<VesselModule {...defaultProps} vessels={[]} />);
    
    // When no vessels, icons should be hidden
    expect(screen.getByTestId('vessel-module-header')).toBeInTheDocument();
  });

  it('renders with vessel selectors in after position', () => {
    render(<VesselModule {...defaultProps} vesselSelectPosition="after" />);
    
    expect(screen.getByTestId('vessel-selectors')).toBeInTheDocument();
  });

  it('starts with grid view when defaultComponent is grid', () => {
    render(<VesselModule {...defaultProps} defaultComponent="grid" />);
    
    expect(screen.getByTestId('vessel-grid')).toBeInTheDocument();
    expect(screen.getByText('Current view: grid')).toBeInTheDocument();
  });

  it('handles empty multiVesselSelects array', () => {
    render(<VesselModule {...defaultProps} multiVesselSelects={[]} />);
    
    expect(screen.getByText('Test Vessel Module')).toBeInTheDocument();
  });

  it('renders modal content correctly when modal is open', () => {
    render(<VesselModule {...defaultProps} />);
    
    // Open modal
    const toggleButton = screen.getByText('Toggle Modal');
    fireEvent.click(toggleButton);
    
    // Modal should contain the same content as main view
    expect(screen.getByTestId('module-modal')).toBeInTheDocument();
  });
});
