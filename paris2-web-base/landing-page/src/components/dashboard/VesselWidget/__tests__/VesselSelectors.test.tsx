import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { VesselSelectors } from '../VesselSelectors';
import { MultiVesselSelectConfig } from '../../../../types/types';

// Mock the VesselSelectGroup component
jest.mock('../VesselSelectGroup', () => ({
  VesselSelectGroup: ({ config, index }: any) => (
    <div data-testid={`vessel-select-group-${index}`}>
      Mock VesselSelectGroup - {config.placeholder}
    </div>
  ),
}));

describe('VesselSelectors Component', () => {
  const mockVesselGroups = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel 1' },
        { vessel_id: 2, name: 'Vessel 2' },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Vessel 3' },
      ],
    },
  ];

  const defaultMultiVesselSelects: MultiVesselSelectConfig[] = [
    {
      placeholder: 'All Vessels',
      width: '300px',
      groups: mockVesselGroups,
      isSearchBoxVisible: true,
      isSelectAllVisible: true,
    },
    {
      placeholder: 'Risk Assessment',
      width: '250px',
      groups: mockVesselGroups,
      isSearchBoxVisible: false,
      isSelectAllVisible: false,
    },
  ];

  const defaultProps = {
    multiVesselSelects: defaultMultiVesselSelects,
    selectStates: [['Vessel 1'], []],
    onSelectChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all vessel select groups', () => {
    render(<VesselSelectors {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-select-group-1')).toBeInTheDocument();
    expect(screen.getByText('Mock VesselSelectGroup - All Vessels')).toBeInTheDocument();
    expect(screen.getByText('Mock VesselSelectGroup - Risk Assessment')).toBeInTheDocument();
  });

  it('returns null when multiVesselSelects is not an array', () => {
    const { container } = render(
      <VesselSelectors
        {...defaultProps}
        multiVesselSelects={null as any}
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('returns null when multiVesselSelects is undefined', () => {
    const { container } = render(
      <VesselSelectors
        {...defaultProps}
        multiVesselSelects={undefined as any}
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('renders empty container when multiVesselSelects is empty array', () => {
    render(<VesselSelectors {...defaultProps} multiVesselSelects={[]} />);
    
    const container = screen.getByRole('generic');
    expect(container).toBeInTheDocument();
    expect(container).toBeEmptyDOMElement();
  });

  it('skips rendering select groups without groups property', () => {
    const selectsWithMissingGroups = [
      {
        placeholder: 'Valid Select',
        width: '300px',
        groups: mockVesselGroups,
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Invalid Select',
        width: '300px',
        groups: null as any,
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ];

    render(
      <VesselSelectors
        {...defaultProps}
        multiVesselSelects={selectsWithMissingGroups}
      />
    );
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-select-group-1')).not.toBeInTheDocument();
    expect(screen.getByText('Mock VesselSelectGroup - Valid Select')).toBeInTheDocument();
    expect(screen.queryByText('Mock VesselSelectGroup - Invalid Select')).not.toBeInTheDocument();
  });

  it('handles single vessel select configuration', () => {
    const singleSelect = [defaultMultiVesselSelects[0]];
    
    render(
      <VesselSelectors
        {...defaultProps}
        multiVesselSelects={singleSelect}
        selectStates={[['Vessel 1']]}
      />
    );
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.queryByTestId('vessel-select-group-1')).not.toBeInTheDocument();
  });

  it('renders with correct container class', () => {
    render(<VesselSelectors {...defaultProps} />);
    
    const container = screen.getByRole('generic');
    expect(container).toHaveClass('ra-vessel-selects-container');
  });

  it('handles empty selectStates array', () => {
    render(
      <VesselSelectors
        {...defaultProps}
        selectStates={[]}
      />
    );
    
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-select-group-1')).toBeInTheDocument();
  });

  it('uses placeholder as key when available', () => {
    render(<VesselSelectors {...defaultProps} />);
    
    // The component should render without errors, indicating keys are working properly
    expect(screen.getByTestId('vessel-select-group-0')).toBeInTheDocument();
    expect(screen.getByTestId('vessel-select-group-1')).toBeInTheDocument();
  });
});
