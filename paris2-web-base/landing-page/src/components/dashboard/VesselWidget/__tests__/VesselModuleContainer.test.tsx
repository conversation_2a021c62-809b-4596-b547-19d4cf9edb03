import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VesselModuleContainer from '../VesselModuleContainer';

// Mock the VesselModule component
jest.mock('../VesselModule', () => {
  return function MockVesselModule({ title, vessels, onRefresh, onSendEmail, onVesselClick }: any) {
    return (
      <div data-testid="vessel-module">
        <div>Mock Vessel Module</div>
        <div>Title: {title}</div>
        <div>Vessels: {vessels?.length || 0}</div>
        <button onClick={onRefresh}>Refresh</button>
        <button onClick={() => onSendEmail({ vessel_ownership_id: 1, risk_id: 123 })}>
          Send Email
        </button>
        <button onClick={() => onVesselClick({ vessel_id: 1 })}>
          Click Vessel
        </button>
      </div>
    );
  };
});

// Mock the useInfiniteQuery hook
jest.mock('../../../../hooks/useInfiniteQuery', () => {
  return jest.fn(() => ({
    data: {
      data: [
        { name: 'Test Vessel 1', vessel_id: 1 },
        { name: 'Test Vessel 2', vessel_id: 2 },
      ],
      pagination: {
        totalItems: 2,
        totalPages: 1,
        page: 1,
        pageSize: 10,
      },
    },
    isLoading: false,
    isFetchingNextPage: false,
    fetchNextPage: jest.fn(),
    refetch: jest.fn(),
  }));
});

// Mock the API functions
jest.mock('../../service/api', () => ({
  fetchVesselOwnerships: jest.fn(() => Promise.resolve([
    {
      id: 1,
      title: 'Test Group',
      vessels: [{ vessel_id: 1, name: 'Test Vessel' }],
    },
  ])),
}));

// Mock the vessel groups
jest.mock('src/services/__mocks__/vesselModuleConfig', () => ({
  vesselGroups2: [
    {
      id: 2,
      title: 'Risk Assessment Group',
      vessels: [{ vessel_id: 2, name: 'Risk Vessel' }],
    },
  ],
}));

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
});

describe('VesselModuleContainer Component', () => {
  const mockFetchFn = jest.fn(() => Promise.resolve({
    data: [],
    pagination: { totalItems: 0, totalPages: 0, page: 1, pageSize: 10 },
  }));

  const defaultProps = {
    title: 'Test Container',
    fetchFn: mockFetchFn,
    tabs: ['All', 'Active'],
    gridComponent: 'bar' as const,
    defaultComponent: 'list' as const,
    cellStyleType: 'default' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders VesselModule with correct props', async () => {
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Mock Vessel Module')).toBeInTheDocument();
    expect(screen.getByText('Title: Test Container')).toBeInTheDocument();
  });

  it('passes vessels data to VesselModule', async () => {
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Vessels: 2')).toBeInTheDocument();
    });
  });

  it('sets IsAllTabVisible to true when tabs array is empty', async () => {
    render(<VesselModuleContainer {...defaultProps} tabs={[]} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
  });

  it('sets IsAllTabVisible to false when tabs array has items', async () => {
    render(<VesselModuleContainer {...defaultProps} tabs={['Tab1', 'Tab2']} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
  });

  it('handles vessel email sending', async () => {
    const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);
    
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
    
    const emailButton = screen.getByText('Send Email');
    emailButton.click();
    
    expect(windowOpenSpy).toHaveBeenCalledWith(
      'https://paris2-dev2.fleetship.com/risk-assessment/approval/123',
      '_blank'
    );
    
    windowOpenSpy.mockRestore();
  });

  it('does not open email when vessel_ownership_id is missing', async () => {
    const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);
    
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
    
    // Mock the email handler to be called with vessel without vessel_ownership_id
    const emailButton = screen.getByText('Send Email');
    
    // Simulate clicking with vessel that has no vessel_ownership_id
    const mockVesselModule = screen.getByTestId('vessel-module');
    const mockOnSendEmail = jest.fn();
    
    // This should not call window.open
    mockOnSendEmail({ risk_id: 123 }); // No vessel_ownership_id
    
    expect(windowOpenSpy).not.toHaveBeenCalled();
    
    windowOpenSpy.mockRestore();
  });

  it('handles vessel click', async () => {
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
    
    const vesselButton = screen.getByText('Click Vessel');
    vesselButton.click();
    
    // Should not throw error
    expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
  });

  it('loads vessel ownerships on mount', async () => {
    const { fetchVesselOwnerships } = require('../../service/api');
    
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(fetchVesselOwnerships).toHaveBeenCalledTimes(1);
    });
  });

  it('sets up multiVesselSelects correctly', async () => {
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
    
    // The component should render without errors, indicating multiVesselSelects was set up
    expect(screen.getByText('Mock Vessel Module')).toBeInTheDocument();
  });

  it('handles refresh action', async () => {
    const useInfiniteQueryMock = require('../../../../hooks/useInfiniteQuery');
    const mockRefetch = jest.fn();
    
    useInfiniteQueryMock.mockReturnValue({
      data: { data: [], pagination: { totalItems: 0, totalPages: 0, page: 1, pageSize: 10 } },
      isLoading: false,
      isFetchingNextPage: false,
      fetchNextPage: jest.fn(),
      refetch: mockRefetch,
    });
    
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByText('Refresh');
    refreshButton.click();
    
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it('passes through additional props', async () => {
    const additionalProps = {
      customProp: 'test value',
      anotherProp: 123,
    };
    
    render(<VesselModuleContainer {...defaultProps} {...additionalProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
  });

  it('handles loading state', async () => {
    const useInfiniteQueryMock = require('../../../../hooks/useInfiniteQuery');
    
    useInfiniteQueryMock.mockReturnValue({
      data: null,
      isLoading: true,
      isFetchingNextPage: false,
      fetchNextPage: jest.fn(),
      refetch: jest.fn(),
    });
    
    render(<VesselModuleContainer {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });
  });

  it('handles error in vessel ownerships fetch', async () => {
    const { fetchVesselOwnerships } = require('../../service/api');
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    fetchVesselOwnerships.mockRejectedValueOnce(new Error('API Error'));

    render(<VesselModuleContainer {...defaultProps} />);

    // Should still render without crashing
    await waitFor(() => {
      expect(screen.getByTestId('vessel-module')).toBeInTheDocument();
    });

    consoleSpy.mockRestore();
  });
});
