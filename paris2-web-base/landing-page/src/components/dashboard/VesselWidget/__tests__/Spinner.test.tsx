import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Spinner from '../Spinner';

describe('Spinner Component', () => {
  it('renders with default props', () => {
    render(<Spinner />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders with custom alignClass prop', () => {
    const customAlignClass = 'custom-align-class';
    render(<Spinner alignClass={customAlignClass} />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders loading text correctly', () => {
    render(<Spinner />);
    
    const loadingText = screen.getByText('Loading...');
    expect(loadingText).toBeInTheDocument();
    expect(loadingText.tagName).toBe('SPAN');
  });

  it('has correct structure', () => {
    const { container } = render(<Spinner />);
    
    const wrapper = container.firstChild;
    expect(wrapper).toBeInTheDocument();
    expect(wrapper).toHaveTextContent('Loading...');
  });

  it('accepts alignClass prop but still renders correctly', () => {
    render(<Spinner alignClass="justify-content-start" />);
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });
});
