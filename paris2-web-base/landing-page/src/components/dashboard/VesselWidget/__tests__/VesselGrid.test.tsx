import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import VesselGrid from '../VesselGrid';
import { Vessel } from '../../../../types/types';

// Mock the useInfiniteScroll hook
jest.mock('../../../../hooks/useInfiniteScroll', () => ({
  useInfiniteScroll: () => ({
    containerRef: { current: null },
    handleScroll: jest.fn(),
  }),
}));

// Mock the Spinner component
jest.mock('../Spinner', () => {
  return function MockSpinner() {
    return <div data-testid="spinner">Loading...</div>;
  };
});

describe('VesselGrid Component', () => {
  const mockVessels: Vessel[] = [
    {
      name: 'Vessel Alpha',
      vesselData: ['data1', 'data2'],
      type: 'cargo',
      vessel_ownership_id: 1,
      risk_id: 101,
      vessel_id: 1,
    },
    {
      name: 'Vessel Beta',
      vesselData: ['data3', 'data4'],
      type: 'tanker',
      vessel_ownership_id: 2,
      risk_id: 102,
      vessel_id: 2,
    },
  ];

  const defaultProps = {
    vessels: mockVessels,
    tableHeaders: ['Name', 'Type', 'Status'],
    badgeColors: ['#ff0000', '#00ff00', '#0000ff'],
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: jest.fn(),
    pagination: {
      totalItems: 10,
      totalPages: 2,
      page: 1,
      pageSize: 5,
    },
    isModal: false,
    gridComponent: 'bar' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading spinner when isLoading is true', () => {
    render(<VesselGrid {...defaultProps} isLoading={true} />);
    
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders "No results found" when vessels array is empty', () => {
    render(<VesselGrid {...defaultProps} vessels={[]} isLoading={false} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('renders bar chart wrapper for default gridComponent', () => {
    render(<VesselGrid {...defaultProps} />);
    
    const wrapper = screen.getByText('No results found').parentElement;
    expect(wrapper).toHaveClass('vessel-bar-chart-wrapper');
  });

  it('renders bar chart wrapper when gridComponent is "bar"', () => {
    render(<VesselGrid {...defaultProps} gridComponent="bar" />);
    
    const wrapper = screen.getByText('No results found').parentElement;
    expect(wrapper).toHaveClass('vessel-bar-chart-wrapper');
  });

  it('renders correctly when gridComponent is "pie"', () => {
    render(<VesselGrid {...defaultProps} gridComponent="pie" />);
    
    // Since pie chart implementation is empty, it should render the bar chart wrapper
    const wrapper = screen.getByText('No results found').parentElement;
    expect(wrapper).toHaveClass('vessel-bar-chart-wrapper');
  });

  it('renders correctly when gridComponent is "dashboard"', () => {
    render(<VesselGrid {...defaultProps} gridComponent="dashboard" />);
    
    // Since dashboard implementation is empty, it should render the bar chart wrapper
    const wrapper = screen.getByText('No results found').parentElement;
    expect(wrapper).toHaveClass('vessel-bar-chart-wrapper');
  });

  it('shows loading indicator when fetching next page', () => {
    render(<VesselGrid {...defaultProps} isFetchingNextPage={true} />);
    
    const loadingIndicators = screen.getAllByTestId('spinner');
    expect(loadingIndicators).toHaveLength(1);
  });

  it('does not show loading indicator when not fetching next page', () => {
    render(<VesselGrid {...defaultProps} isFetchingNextPage={false} />);
    
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });

  it('handles pagination correctly when hasNextPage is true', () => {
    const pagination = {
      totalItems: 20,
      totalPages: 4,
      page: 2,
      pageSize: 5,
    };
    
    render(<VesselGrid {...defaultProps} pagination={pagination} />);
    
    // Component should render without errors
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('handles pagination correctly when hasNextPage is false', () => {
    const pagination = {
      totalItems: 10,
      totalPages: 2,
      page: 2,
      pageSize: 5,
    };
    
    render(<VesselGrid {...defaultProps} pagination={pagination} />);
    
    // Component should render without errors
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('handles null pagination', () => {
    render(<VesselGrid {...defaultProps} pagination={null as any} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('handles undefined pagination', () => {
    render(<VesselGrid {...defaultProps} pagination={undefined as any} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('renders with modal class when isModal is true', () => {
    const { container } = render(<VesselGrid {...defaultProps} isModal={true} />);
    
    const rootElement = container.firstChild;
    expect(rootElement).toHaveClass('vessel-grid-root');
  });

  it('renders with correct root class', () => {
    const { container } = render(<VesselGrid {...defaultProps} />);
    
    const rootElement = container.firstChild;
    expect(rootElement).toHaveClass('vessel-grid-root');
  });

  it('passes through additional props via rest parameter', () => {
    const additionalProps = {
      'data-testid': 'custom-grid',
      className: 'custom-class',
    };
    
    render(<VesselGrid {...defaultProps} {...additionalProps} />);
    
    const gridElement = screen.getByTestId('custom-grid');
    expect(gridElement).toBeInTheDocument();
  });

  it('handles vessels with different data structures', () => {
    const diverseVessels: Vessel[] = [
      {
        name: 'Vessel 1',
        vesselData: [],
        type: 'cargo',
      },
      {
        name: 'Vessel 2',
        vesselData: ['data'],
        type: 'tanker',
        vessel_id: 123,
      },
    ];
    
    render(<VesselGrid {...defaultProps} vessels={diverseVessels} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('renders correctly with minimal required props', () => {
    const minimalProps = {
      vessels: [],
      tableHeaders: [],
      badgeColors: [],
      fetchNextPage: jest.fn(),
      pagination: null,
    };
    
    render(<VesselGrid {...minimalProps} />);
    
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });
});
