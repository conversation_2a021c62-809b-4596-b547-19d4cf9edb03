import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { VesselModuleHeader } from '../VesselModuleHeader';

describe('VesselModuleHeader Component', () => {
  const defaultProps = {
    title: 'Test Vessel Module',
    viewMode: 'list' as const,
    isModal: false,
    IsiconRenderVisible: true,
    IsenLargeIconVisible: true,
    onViewModeChange: jest.fn(),
    onToggleModal: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders title correctly', () => {
    render(<VesselModuleHeader {...defaultProps} />);
    
    expect(screen.getByText('Test Vessel Module')).toBeInTheDocument();
    expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('Test Vessel Module');
  });

  it('renders view toggle buttons when IsiconRenderVisible is true', () => {
    render(<VesselModuleHeader {...defaultProps} />);
    
    expect(screen.getByLabelText('Grid view')).toBeInTheDocument();
    expect(screen.getByLabelText('List view')).toBeInTheDocument();
  });

  it('does not render view toggle buttons when IsiconRenderVisible is false', () => {
    render(<VesselModuleHeader {...defaultProps} IsiconRenderVisible={false} />);
    
    expect(screen.queryByLabelText('Grid view')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('List view')).not.toBeInTheDocument();
  });

  it('calls onViewModeChange when grid button is clicked', () => {
    const onViewModeChangeMock = jest.fn();
    render(<VesselModuleHeader {...defaultProps} onViewModeChange={onViewModeChangeMock} />);
    
    const gridButton = screen.getByLabelText('Grid view');
    fireEvent.click(gridButton);
    
    expect(onViewModeChangeMock).toHaveBeenCalledWith('grid');
  });

  it('calls onViewModeChange when list button is clicked', () => {
    const onViewModeChangeMock = jest.fn();
    render(<VesselModuleHeader {...defaultProps} onViewModeChange={onViewModeChangeMock} />);
    
    const listButton = screen.getByLabelText('List view');
    fireEvent.click(listButton);
    
    expect(onViewModeChangeMock).toHaveBeenCalledWith('list');
  });

  it('applies active class to grid button when viewMode is grid', () => {
    render(<VesselModuleHeader {...defaultProps} viewMode="grid" />);
    
    const gridButton = screen.getByLabelText('Grid view');
    expect(gridButton).toHaveClass('active');
  });

  it('applies active class to list button when viewMode is list', () => {
    render(<VesselModuleHeader {...defaultProps} viewMode="list" />);
    
    const listButton = screen.getByLabelText('List view');
    expect(listButton).toHaveClass('active');
  });

  it('renders enlarge button when IsenLargeIconVisible is true and not in modal', () => {
    render(<VesselModuleHeader {...defaultProps} IsenLargeIconVisible={true} isModal={false} />);

    const enlargeIcon = screen.getByLabelText('Minimize view');
    expect(enlargeIcon).toBeInTheDocument();
  });

  it('renders minimize button when in modal mode', () => {
    render(<VesselModuleHeader {...defaultProps} isModal={true} />);

    const minimizeIcon = screen.getByLabelText('Minimize view');
    expect(minimizeIcon).toBeInTheDocument();
  });

  it('does not render enlarge button when IsenLargeIconVisible is false', () => {
    render(<VesselModuleHeader {...defaultProps} IsenLargeIconVisible={false} />);
    
    expect(screen.queryByLabelText('Enlarge')).not.toBeInTheDocument();
  });

  it('calls onToggleModal when enlarge button is clicked', () => {
    const onToggleModalMock = jest.fn();
    render(<VesselModuleHeader {...defaultProps} onToggleModal={onToggleModalMock} />);

    const enlargeButton = screen.getByLabelText('Minimize view');
    fireEvent.click(enlargeButton);

    expect(onToggleModalMock).toHaveBeenCalledTimes(1);
  });

  it('calls onToggleModal when minimize button is clicked', () => {
    const onToggleModalMock = jest.fn();
    render(<VesselModuleHeader {...defaultProps} isModal={true} onToggleModal={onToggleModalMock} />);

    const minimizeButton = screen.getByLabelText('Minimize view');
    fireEvent.click(minimizeButton);

    expect(onToggleModalMock).toHaveBeenCalledTimes(1);
  });

  it('renders with minimal props', () => {
    const minimalProps = {
      title: 'Minimal Title',
      viewMode: 'list' as const,
      isModal: false,
      onViewModeChange: jest.fn(),
      onToggleModal: jest.fn(),
    };
    
    render(<VesselModuleHeader {...minimalProps} />);
    
    expect(screen.getByText('Minimal Title')).toBeInTheDocument();
  });
});
