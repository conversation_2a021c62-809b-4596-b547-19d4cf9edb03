import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { VesselDropdownMenu } from '../VesselDropdownMenu';
import { VesselGroup } from '../../../../types/types';

describe('VesselDropdownMenu Component', () => {
  const mockGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel Alpha' },
        { vessel_id: 2, name: 'Vessel Beta' },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Ship Gamma' },
        { vessel_id: 4, name: 'Ship Delta' },
      ],
    },
  ];

  const defaultProps = {
    searchTerm: '',
    onSearchChange: jest.fn(),
    filteredGroups: mockGroups,
    selectedVessels: ['Vessel Alpha'],
    onToggleVessel: jest.fn(),
    onToggleGroup: jest.fn(),
    isAllSelected: false,
    onToggleAll: jest.fn(),
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders search input when isSearchBoxVisible is true', () => {
    render(<VesselDropdownMenu {...defaultProps} />);
    
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    expect(screen.getByLabelText('Search vessels')).toBeInTheDocument();
  });

  it('does not render search input when isSearchBoxVisible is false', () => {
    render(<VesselDropdownMenu {...defaultProps} isSearchBoxVisible={false} />);
    
    expect(screen.queryByPlaceholderText('Search')).not.toBeInTheDocument();
  });

  it('renders select all button when isSelectAllVisible is true', () => {
    render(<VesselDropdownMenu {...defaultProps} />);
    
    expect(screen.getByText('Select All')).toBeInTheDocument();
  });

  it('does not render select all button when isSelectAllVisible is false', () => {
    render(<VesselDropdownMenu {...defaultProps} isSelectAllVisible={false} />);
    
    expect(screen.queryByText('Select All')).not.toBeInTheDocument();
  });

  it('renders all vessel groups and vessels', () => {
    render(<VesselDropdownMenu {...defaultProps} />);
    
    expect(screen.getByText('Vessel Alpha')).toBeInTheDocument();
    expect(screen.getByText('Vessel Beta')).toBeInTheDocument();
    expect(screen.getByText('Ship Gamma')).toBeInTheDocument();
    expect(screen.getByText('Ship Delta')).toBeInTheDocument();
  });

  it('calls onSearchChange when search input changes', () => {
    const onSearchChangeMock = jest.fn();
    render(<VesselDropdownMenu {...defaultProps} onSearchChange={onSearchChangeMock} />);
    
    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    
    expect(onSearchChangeMock).toHaveBeenCalledWith('test search');
  });

  it('displays current search term in input', () => {
    render(<VesselDropdownMenu {...defaultProps} searchTerm="alpha" />);
    
    const searchInput = screen.getByDisplayValue('alpha');
    expect(searchInput).toBeInTheDocument();
  });

  it('calls onToggleVessel when vessel checkbox is clicked', () => {
    const onToggleVesselMock = jest.fn();
    render(<VesselDropdownMenu {...defaultProps} onToggleVessel={onToggleVesselMock} />);
    
    const vesselCheckbox = screen.getByLabelText('Vessel Alpha');
    fireEvent.click(vesselCheckbox);
    
    expect(onToggleVesselMock).toHaveBeenCalledWith('Vessel Alpha');
  });

  it('shows correct checkbox states for selected vessels', () => {
    render(<VesselDropdownMenu {...defaultProps} selectedVessels={['Vessel Alpha', 'Ship Gamma']} />);
    
    const alphaCheckbox = screen.getByRole('checkbox', { name: 'Vessel Alpha' });
    const betaCheckbox = screen.getByRole('checkbox', { name: 'Vessel Beta' });
    const gammaCheckbox = screen.getByRole('checkbox', { name: 'Ship Gamma' });
    
    expect(alphaCheckbox).toBeChecked();
    expect(betaCheckbox).not.toBeChecked();
    expect(gammaCheckbox).toBeChecked();
  });

  it('calls onToggleAll when select all button is clicked', () => {
    const onToggleAllMock = jest.fn();
    render(<VesselDropdownMenu {...defaultProps} onToggleAll={onToggleAllMock} />);
    
    const selectAllButton = screen.getByText('Select All');
    fireEvent.click(selectAllButton);
    
    expect(onToggleAllMock).toHaveBeenCalledTimes(1);
  });

  it('shows "Clear All" when isAllSelected is true', () => {
    render(<VesselDropdownMenu {...defaultProps} isAllSelected={true} />);
    
    expect(screen.getByText('Clear All')).toBeInTheDocument();
    expect(screen.queryByText('Select All')).not.toBeInTheDocument();
  });

  it('shows "Select All" when isAllSelected is false', () => {
    render(<VesselDropdownMenu {...defaultProps} isAllSelected={false} />);
    
    expect(screen.getByText('Select All')).toBeInTheDocument();
    expect(screen.queryByText('Clear All')).not.toBeInTheDocument();
  });

  it('renders empty groups correctly', () => {
    render(<VesselDropdownMenu {...defaultProps} filteredGroups={[]} />);
    
    // Should still render the container but with no vessels
    expect(screen.queryByText('Vessel Alpha')).not.toBeInTheDocument();
  });

  it('handles groups with no vessels', () => {
    const emptyGroups: VesselGroup[] = [
      {
        id: 1,
        title: 'Empty Group',
        vessels: [],
      },
    ];
    
    render(<VesselDropdownMenu {...defaultProps} filteredGroups={emptyGroups} />);
    
    expect(screen.queryByText('Vessel Alpha')).not.toBeInTheDocument();
  });

  it('renders with minimal props', () => {
    const minimalProps = {
      searchTerm: '',
      onSearchChange: jest.fn(),
      filteredGroups: [],
      selectedVessels: [],
      onToggleVessel: jest.fn(),
      onToggleGroup: jest.fn(),
      isAllSelected: false,
      onToggleAll: jest.fn(),
    };
    
    render(<VesselDropdownMenu {...minimalProps} />);
    
    // Should render without search and select all components
    expect(screen.queryByPlaceholderText('Search')).not.toBeInTheDocument();
    expect(screen.queryByText('Select All')).not.toBeInTheDocument();
  });

  it('has correct accessibility attributes', () => {
    render(<VesselDropdownMenu {...defaultProps} />);
    
    const searchInput = screen.getByLabelText('Search vessels');
    expect(searchInput).toHaveAttribute('aria-label', 'Search vessels');
    
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).toHaveAttribute('type', 'checkbox');
    });
  });
});
