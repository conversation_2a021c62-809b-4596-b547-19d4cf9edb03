import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { VesselSelectGroup } from '../VesselSelectGroup';
import { VesselGroup } from '../../../../types/types';

// Mock the VesselDropdown component
jest.mock('../VesselDropdown', () => ({
  VesselDropdown: ({ placeholder, width, selectedVessels, onSelectionChange }: any) => (
    <div data-testid="vessel-dropdown">
      <span>Mock VesselDropdown</span>
      <span>Placeholder: {placeholder}</span>
      <span>Width: {width}</span>
      <span>Selected: {selectedVessels.join(', ')}</span>
      <button onClick={() => onSelectionChange(['new-selection'])}>
        Change Selection
      </button>
    </div>
  ),
}));

describe('VesselSelectGroup Component', () => {
  const mockGroups: VesselGroup[] = [
    {
      id: 1,
      title: 'Group 1',
      vessels: [
        { vessel_id: 1, name: 'Vessel 1' },
        { vessel_id: 2, name: 'Vessel 2' },
      ],
    },
    {
      id: 2,
      title: 'Group 2',
      vessels: [
        { vessel_id: 3, name: 'Vessel 3' },
      ],
    },
  ];

  const defaultProps = {
    index: 0,
    config: {
      placeholder: 'Select Vessels',
      width: '300px',
    },
    selectedVessels: ['Vessel 1'],
    groups: mockGroups,
    onChange: jest.fn(),
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders VesselDropdown with correct props', () => {
    render(<VesselSelectGroup {...defaultProps} />);
    
    expect(screen.getByTestId('vessel-dropdown')).toBeInTheDocument();
    expect(screen.getByText('Mock VesselDropdown')).toBeInTheDocument();
    expect(screen.getByText('Placeholder: Select Vessels')).toBeInTheDocument();
    expect(screen.getByText('Width: 300px')).toBeInTheDocument();
    expect(screen.getByText('Selected: Vessel 1')).toBeInTheDocument();
  });

  it('returns null when groups is missing', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const { container } = render(
      <VesselSelectGroup
        {...defaultProps}
        groups={null as any}
      />
    );
    
    expect(container.firstChild).toBeNull();
    expect(consoleSpy).toHaveBeenCalledWith('VesselSelectGroup: `groups` prop is missing.');
    
    consoleSpy.mockRestore();
  });

  it('returns null when groups is undefined', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const { container } = render(
      <VesselSelectGroup
        {...defaultProps}
        groups={undefined as any}
      />
    );
    
    expect(container.firstChild).toBeNull();
    expect(consoleSpy).toHaveBeenCalledWith('VesselSelectGroup: `groups` prop is missing.');
    
    consoleSpy.mockRestore();
  });

  it('calls onChange with correct parameters when selection changes', () => {
    const onChangeMock = jest.fn();
    render(<VesselSelectGroup {...defaultProps} onChange={onChangeMock} />);
    
    const changeButton = screen.getByText('Change Selection');
    changeButton.click();
    
    expect(onChangeMock).toHaveBeenCalledWith(0, ['new-selection']);
  });

  it('handles empty selectedVessels array', () => {
    render(<VesselSelectGroup {...defaultProps} selectedVessels={[]} />);
    
    expect(screen.getByText('Selected:')).toBeInTheDocument();
  });

  it('handles multiple selected vessels', () => {
    render(
      <VesselSelectGroup
        {...defaultProps}
        selectedVessels={['Vessel 1', 'Vessel 2', 'Vessel 3']}
      />
    );
    
    expect(screen.getByText('Selected: Vessel 1, Vessel 2, Vessel 3')).toBeInTheDocument();
  });

  it('uses default width when not provided in config', () => {
    render(
      <VesselSelectGroup
        {...defaultProps}
        config={{ placeholder: 'Test' }}
      />
    );
    
    expect(screen.getByText('Width: 200px')).toBeInTheDocument();
  });

  it('passes isSearchBoxVisible prop correctly', () => {
    render(<VesselSelectGroup {...defaultProps} isSearchBoxVisible={false} />);
    
    expect(screen.getByTestId('vessel-dropdown')).toBeInTheDocument();
  });

  it('passes isSelectAllVisible prop correctly', () => {
    render(<VesselSelectGroup {...defaultProps} isSelectAllVisible={false} />);
    
    expect(screen.getByTestId('vessel-dropdown')).toBeInTheDocument();
  });

  it('renders with correct wrapper class', () => {
    const { container } = render(<VesselSelectGroup {...defaultProps} />);
    
    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass('raSelectWrapper');
  });

  it('handles different index values', () => {
    const onChangeMock = jest.fn();
    render(<VesselSelectGroup {...defaultProps} index={5} onChange={onChangeMock} />);
    
    const changeButton = screen.getByText('Change Selection');
    changeButton.click();
    
    expect(onChangeMock).toHaveBeenCalledWith(5, ['new-selection']);
  });

  it('memoizes correctly and does not re-render unnecessarily', () => {
    const { rerender } = render(<VesselSelectGroup {...defaultProps} />);
    
    const initialElement = screen.getByTestId('vessel-dropdown');
    
    // Re-render with same props
    rerender(<VesselSelectGroup {...defaultProps} />);
    
    const afterRerenderElement = screen.getByTestId('vessel-dropdown');
    expect(afterRerenderElement).toBeInTheDocument();
  });
});
