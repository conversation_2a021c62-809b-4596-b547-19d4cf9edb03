import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ModuleModal } from '../ModuleModal';

describe('ModuleModal Component', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    size: {
      width: '800px',
      height: '600px',
    },
    children: <div>Modal Content</div>,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when isOpen is true', () => {
    render(<ModuleModal {...defaultProps} />);
    
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('does not render when isOpen is false', () => {
    render(<ModuleModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
  });

  it('calls onClose when overlay is clicked', () => {
    const onCloseMock = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onCloseMock} />);

    const overlay = screen.getByRole('button', { name: 'Close modal' });
    fireEvent.click(overlay);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('does not call onClose when modal content is clicked', () => {
    const onCloseMock = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onCloseMock} />);
    
    const modalContent = screen.getByText('Modal Content');
    fireEvent.click(modalContent);
    
    expect(onCloseMock).not.toHaveBeenCalled();
  });

  it('applies correct size styles', () => {
    const customSize = {
      width: '1000px',
      height: '800px',
    };
    render(<ModuleModal {...defaultProps} size={customSize} />);

    const modalContent = screen.getByText('Modal Content').parentElement;
    expect(modalContent).toHaveStyle({
      '--modal-width': '1000px',
      '--modal-height': '800px',
    });
  });

  it('renders children correctly', () => {
    const customChildren = (
      <div>
        <h1>Custom Title</h1>
        <p>Custom content</p>
      </div>
    );
    
    render(<ModuleModal {...defaultProps}>{customChildren}</ModuleModal>);
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom content')).toBeInTheDocument();
  });

  it('has correct accessibility attributes', () => {
    render(<ModuleModal {...defaultProps} />);

    const overlay = screen.getByRole('button', { name: 'Close modal' });
    expect(overlay).toBeInTheDocument();
    expect(overlay).toHaveAttribute('aria-label', 'Close modal');
    expect(overlay).toHaveAttribute('tabIndex', '0');
  });

  it('handles multiple children', () => {
    const multipleChildren = (
      <>
        <div>First child</div>
        <div>Second child</div>
        <span>Third child</span>
      </>
    );
    
    render(<ModuleModal {...defaultProps}>{multipleChildren}</ModuleModal>);
    
    expect(screen.getByText('First child')).toBeInTheDocument();
    expect(screen.getByText('Second child')).toBeInTheDocument();
    expect(screen.getByText('Third child')).toBeInTheDocument();
  });

  it('prevents event bubbling when clicking inside modal', () => {
    const onCloseMock = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onCloseMock} />);

    const modalContent = screen.getByText('Modal Content');
    fireEvent.click(modalContent);

    expect(onCloseMock).not.toHaveBeenCalled();
  });
});
