import React, { useMemo } from "react";
import StatCard from "./StatCard/StatCard";
import StatusIndicatorCard from "./StatusIndicatorCard/StatusIndicatorCard";
import "./Dashboard.scss";
import { Vessel } from "../../../../types/types";


interface DashboardProps {
  vessels?: Vessel[];
  badgeColors: string[];
}

interface StatusInfo {
  label: string;
  value: number;
  color: string;
}

const DashboardSection: React.FC<{ title: string; statuses: StatusInfo[] }> = ({
  title,
  statuses,
}) => (
  <div className="dashboard-section">
    <h3 className="section-title">{title.toUpperCase()}</h3>
    <div className="status-grid">
      {statuses.map((status, index) => (
        <StatusIndicatorCard
          key={index}
          value={status.value}
          label={status.label}
          color={status.color}
        />
      ))}
    </div>
  </div>
);


const defaultProps: Pick<DashboardProps, "vessels" | "badgeColors"> = {
  vessels: [],
  badgeColors: ["#4caf50", "#8bc34a", "#fbc02d", "#f44336"],
};

const Dashboard: React.FC<DashboardProps> = (props) => {
  const { vessels, badgeColors } = { ...defaultProps, ...props };

  const { topStats, groupedStatusData } = useMemo(() => {
    if (!vessels || vessels.length === 0) {
      return { topStats: [], groupedStatusData: [] };
    }

    const totalSubmitted = vessels.length;
    let unassignedCount = 0;

    // This will hold the data grouped by Level of R.A.
    // e.g., { Critical: { Approved: 2, Pending: 1, ... }, Special: { ... } }
    const statusGroups: { [level: string]: { [status: string]: number } } = {};

    vessels.forEach((vessel) => {
      const levelOfRa = (vessel.vesselData[1] as string) || "Unknown";
      const status = (vessel.vesselData[2] as string) || "Unknown";

      if (levelOfRa === "Unassigned") {
        unassignedCount++;
      }

      if (!statusGroups[levelOfRa]) {
        statusGroups[levelOfRa] = {};
      }

      if (!statusGroups[levelOfRa][status]) {
        statusGroups[levelOfRa][status] = 0;
      }
     
      statusGroups[levelOfRa][status]++;
    });

    const formattedTopStats = [
      { title: "Total Risk Assessment Submitted", value: totalSubmitted },
      { title: "Unassigned Risk Assessments", value: unassignedCount },
    ];

    const statusToColorMap: { [key: string]: string } = {
      Approved: badgeColors[0],
      "Approved with Condition": badgeColors[1],
      Pending: badgeColors[2],
      Rejected: badgeColors[3],
    };

    const formattedGroupedData = Object.entries(statusGroups)
      .filter(([level]) => level !== "Unassigned") 
      .map(([level, statuses]) => ({
        title: level,
        statuses: Object.entries(statuses).map(([status, count]) => ({
          label: status,
          value: count,
          color: statusToColorMap[status] || "#808080",
        })),
      }));

    return {
      topStats: formattedTopStats,
      groupedStatusData: formattedGroupedData,
    };
  }, [vessels, badgeColors]);

  return (
    <div className="dashboard-container">
      <div className="dashboard-grid">
        {topStats.map((stat, index) => (
          <StatCard key={index} title={stat.title} value={stat.value} />
        ))}
      </div>

      {groupedStatusData.map((group) => (
        <DashboardSection
          key={group.title}
          title={group.title}
          statuses={group.statuses}
        />
      ))}
    </div>
  );
};

export default Dashboard;
