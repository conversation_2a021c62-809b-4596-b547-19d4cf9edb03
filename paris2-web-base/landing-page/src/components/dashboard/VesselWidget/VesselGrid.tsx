import React, { useMemo } from "react";
import { VesselGridProps, VesselDisplayProps } from "../../../types/types";
import { useInfiniteScroll } from "../../../hooks/useInfiniteScroll";
import Spinner from "./Spinner";
import VesselBarChart from "./VesselBarChart";
import Dynamic3DDonutChart from "./Dynamic3DDonutChart";
import "../styles/scss/VesselGrid.scss";
import Dashboard from "./DashboardGrid/Dashboard";

// --- ADAPTER COMPONENT for the Donut Chart ---
// This component's job is to transform the data from the format VesselGrid
// receives into the format that Dynamic3DDonutChart requires.
const DonutChartAdapter = (props: VesselDisplayProps) => {
  // This transformation logic creates the `chartData` array in the exact
  // format required by `Dynamic3DDonutChart`.
  const chartData = useMemo(() => {
    if (!props.vessels) return [];
    return props.vessels.map((vessel, index) => ({
      label: vessel.name,
      // Data transformation: Summing up the vessel's data points.
      // You can adjust this logic based on what you want the chart to represent.
      value: vessel.vesselData.reduce((sum, current) => sum + current, 0),
      color: props.badgeColors[index % props.badgeColors.length],
      url: `/vessel/${vessel.name.toLowerCase().replace(/\s+/g, "-")}`,
    }));
  }, [props.vessels, props.badgeColors]);

  // Render the donut chart, wrapped in the padded div.
  return (
    <div
      style={{
        padding: "2rem",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
      }}
    >
      <Dynamic3DDonutChart data={chartData} />
    </div>
  );
};

export default function VesselGrid({
  vessels,
  tableHeaders,
  badgeColors,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  isModal,
  gridComponent = "bar",
  ...rest
}: VesselGridProps) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;

  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="spinner-container">
          <Spinner />
        </div>
      );
    }
    if (vessels.length === 0) {
      return <div className="no-results-cell">No results found</div>;
    }

    // --- DYNAMIC RENDERING LOGIC ---
    if (gridComponent === "pie") {
      // If the prop says 'pie', render the Donut Chart via the adapter
      return (
        <DonutChartAdapter
          vessels={vessels}
          tableHeaders={tableHeaders}
          badgeColors={badgeColors}
          {...rest}
        />
      );
    }

    if (gridComponent === "dashboard") {
      // If the prop is 'dashboard', render your DashboardComponent
      // Pass the necessary props like vessels, colors, etc.
     
      return (
        <Dashboard
          vessels={vessels}
          tableHeaders={tableHeaders}
          badgeColors={badgeColors}
        />
      );
    }

    // Otherwise, render the Bar Chart by default
    const chartData = vessels.map((vessel) => {
      const values: Record<string, number | string> = { name: vessel.name };
      tableHeaders
        .filter((h) => h !== "Vessel" && h !== "Action")
        .forEach((header, idx) => {
          values[header] = vessel.vesselData[idx];
        });
      return values;
    });

    return (
      <div className="vessel-bar-chart-wrapper">
        <VesselBarChart
          vessels={chartData}
          valueHeaders={tableHeaders.filter(
            (h) => h !== "Vessel" && h !== "Action",
          )}
          badgeColors={badgeColors}
          valueDomain={[0, 250]}
          isModal={isModal}
        />
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      onScroll={handleScroll}
      className="vessel-grid-root"
    >
      {renderContent()}
      {isFetchingNextPage && (
        <div className="loading-indicator">
          <Spinner />
        </div>
      )}
    </div>
  );
}
