import { useMemo } from "react";
import * as d3 from "d3";

interface D3ChartConfig {
  vessels: any[];
  valueHeaders: string[];
  badgeColors: string[];
  valueDomain: [number, number];
  chartWidth: number;
  heightPerBar: number;
  margin: { top: number; right: number; bottom: number; left: number };
}

/**
 * This hook encapsulates all D3 calculations.
 * It takes raw data and dimensions and returns the scales, colors, and processed
 * data needed for rendering. D3 is used here only for its math utilities,
 * not for DOM manipulation.
 */
export const useD3Chart = ({
  vessels,
  valueHeaders,
  badgeColors,
  valueDomain,
  chartWidth,
  heightPerBar,
  margin,
}: D3ChartConfig) => {
  const chartHeight = useMemo(
    () => vessels.length * heightPerBar,
    [vessels.length, heightPerBar],
  );
  const totalHeight = chartHeight + margin.top + margin.bottom;

  // X Scale - Linear scale for bar values
  const xScale = useMemo(
    () =>
      d3
        .scaleLinear()
        .domain(valueDomain)
        .range([0, chartWidth - margin.left - margin.right])
        .clamp(true),
    [valueDomain, chartWidth, margin],
  );

  // Y Scale - Band scale for vessel names
  const yScale = useMemo(
    () =>
      d3
        .scaleBand()
        .domain(vessels.map((d) => d.name))
        .range([margin.top, chartHeight + margin.top])
        .padding(0.4),
    [vessels, chartHeight, margin.top],
  );

  // Color Scales
  const barColorScale = useMemo(
    () =>
      d3.scaleOrdinal<string, string>().domain(valueHeaders).range(badgeColors),
    [valueHeaders, badgeColors],
  );

  const textColorScale = useMemo(() => {
    const getTextColor = (bg: string) => (bg === "#fbc02d" ? "black" : "white");
    return d3
      .scaleOrdinal<string, string>()
      .domain(valueHeaders)
      .range(badgeColors.map(getTextColor));
  }, [valueHeaders, badgeColors]);

  // Process data for stacked bars
  const stackedBarData = useMemo(
    () =>
      vessels.map((vessel) => {
        let xOffset = 0;
        const segments = valueHeaders
          .map((key) => {
            const value = (vessel[key] as number) || 0;
            const segment = {
              key,
              value,
              x: xScale(xOffset),
              width: xScale(value),
              y: yScale(vessel.name)!,
              height: yScale.bandwidth(),
              vesselName: vessel.name,
            };
            xOffset += value;
            return segment;
          })
          .filter((segment) => segment.value > 0);
        return { vesselName: vessel.name, segments };
      }),
    [vessels, valueHeaders, xScale, yScale],
  );

  return {
    xScale,
    yScale,
    barColorScale,
    textColorScale,
    stackedBarData,
    chartHeight,
    totalHeight,
  };
};
