import React from "react";
import { Row, Col } from "react-bootstrap";
import warningError from '../assets/images/warning-browser.svg';
import firefoxLogo from '../assets/images/firefox-logo.svg';
import chromeLogo from '../assets/images/chrome-logo.svg';
import edgeLogo from '../assets/images/edge-logo.svg';
import safariLogo from '../assets/images/safari-logo.svg';
import './styles/browser-not-supported.scss';
import { isMobile } from "src/utils/utility";

const browserList = [
    {
        title: 'Google Chrome',
        downloadLink: 'https://www.google.com/chrome/',
        logo: chromeLogo,
    },
    {
        title: 'Mozilla Firefox',
        downloadLink: 'https://www.mozilla.org/en-US/firefox/new/',
        logo: firefoxLogo,
    },
    {
        title: 'Apple Safari',
        downloadLink: 'https://www.apple.com/safari/',
        logo: safariLogo,
    },
    {
        title: 'Microsoft Edge',
        downloadLink: 'https://www.microsoft.com/en-us/edge',
        logo: edgeLogo,
    },
];
function getWarningMessage(isMobile: boolean) {
    if (isMobile) {
        return "We recommend you to please update your browser or download any of the browsers from App Store or Play Store";
    }

    return "We recommend updating your browser or downloading one of the browsers mentioned below:";
}
const BrowserNotSupported = () => {
    const isMobileBrowser = isMobile();
    return (
        <div className="browser-not-supported-container mt-5 text-center">
            <Row className="justify-content-center">
                <Col md={6} xs={11} >
                    <img
                        src={warningError}
                        alt="Warning Icon"
                        className="mb-3"
                    />
                    <h3>Your browser version is not supported</h3>
                    <p className="mt-3">{getWarningMessage(isMobileBrowser)}</p>
                    <Row className="mt-5">
                        {
                            browserList.map((browser) => {
                                return (
                                    <Col key={browser.title} md={3} xs={6} className="mb-3">
                                        <img
                                            src={browser.logo}
                                            alt={browser.title}
                                            style={{ width: "50px" }}
                                        />
                                        <p className="browser-title mb-1 mt-1">{browser.title}</p>
                                        {!isMobileBrowser && (<a className="download-link" href={browser.downloadLink} target="_blank">
                                            Download
                                        </a>)}
                                    </Col>
                                )
                            })
                        }
                    </Row>
                </Col>
            </Row>
        </div>
    );
};

export default BrowserNotSupported;
