import React from 'react';
import { KeycloakProps } from '../types/keycloak';
import './styles/dashboard.scss';
import { isOwnerOrRegisteredOwner, isSeafarer } from '../utils/roles';
import { DefaultDashboard } from '../components/dashboard/DefaultDashboard';
import { OwnerDashboard } from '../components/dashboard/OwnerDashboard';
import { SeafarerDashboard } from '../components/dashboard/SeafarerDashboard';

function Dashboard({ keycloak }: KeycloakProps) {
  console.log(keycloak, 'keycloak');

  if (isSeafarer(keycloak)) {
    return <SeafarerDashboard keycloak={keycloak} />;
  }
  if (isOwnerOrRegisteredOwner(keycloak)) {
    return <OwnerDashboard keycloak={keycloak} />;
  }
  return <DefaultDashboard keycloak={keycloak} />;
}

export { Dashboard };
