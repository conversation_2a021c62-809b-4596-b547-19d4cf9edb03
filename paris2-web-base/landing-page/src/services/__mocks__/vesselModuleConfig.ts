import { fetchVesselOwnerships } from "src/components/dashboard/service/api";
import { MultiVesselSelectConfig, VesselGroup, VesselGroupOption } from "../../types/types";

export const vesselGroups1: VesselGroup[] = [
  {
    title: "My Vessels",
    vessels: [
      { vessel_id: 1, name: "Bochem Casablanca" },
      { vessel_id: 2, name: "fleet" },
      { vessel_id: 3, name: "Cyan Nova" },
      { vessel_id: 4, name: "<PERSON><PERSON><PERSON>" },
      { vessel_id: 5, name: "Sunda" }
    ]
  },
  {
    title: "Other Vessels",
    vessels: [
      { vessel_id: 6, name: "Bochem Ghent" },
      { vessel_id: 7, name: "Bochem Mumbai" },
      { vessel_id: 8, name: "Bochem Oslo" },
      { vessel_id: 9, name: "Bochem Shanghai" }
    ]
  }
]


export const vesselGroups2: VesselGroupOption[] = [
  {
    title: "",
    vessels: [
      { vessel_id: -1, name: "Unassigned" },
      { vessel_id: 3, name: "<PERSON>" },
      { vessel_id: 2, name: "Special" },
      { vessel_id: 4, name: "Level 1 RA" }
    ]
  }
]


export const multiVesselSelectsSet1: MultiVesselSelectConfig[] = [
  {
    placeholder: "My Vessels",
    width: "350px",
    groups: vesselGroups1,
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  },
];

export const multiVesselSelectsSet2 = [
  {
    placeholder: "My Vessels",
    width: "340px",
    groups: vesselGroups1,
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  },
  {
    placeholder: "All Level of R.A. ",
    width: "340px",
    groups: vesselGroups2,
    isSearchBoxVisible: false,
    isSelectAllVisible: false,
  },
];

export const tableHeaders1 = [
  "Vessel",
  "Over Due",
  "Due within 30 days",
  "Due within 60 days",
  // "others",
  "Action",
];

export const tableHeaders2 = [
  "Vessel",
  "Overdue",
  "Due within 30 days",
  // "Due within 60 days",
  "Others",
  "Action",
];

export const tableHeaders3 = [
  "Vessel",
  "Task Required",
  "Level of R.A.",
  // "Due within 60 days",
  "Status",
  "Action",
];

export const badgeColors1 = ["#d80e61", "#fbc02d", "#27a527"];
export const badgeColors2 = ["#d80e61", "#fbc02d", "#31a1bd"];
