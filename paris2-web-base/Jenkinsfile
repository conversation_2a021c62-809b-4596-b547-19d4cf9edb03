final ENV_GIT_BRANCH = env.BRANCH
final ENV_STAGE_NAME = env.stageName
final ENV_SKIP_TESTS = env.skipTests
final ENV_SKIP_BUILD = env.skipBuild
final ENV_SOURCE_BRANCH = env.sourcebranch
final ENV_TARGET_BRANCH = env.targetbranch
final ENV_STATE = env.state
final ENV_GIT_REPO = env.gitRepo
ENV_ACCOUNT_ID = env.account
final ENV_REGION = env.region

def accountId = ENV_ACCOUNT_ID
def region = ENV_REGION
def stageName = ENV_STAGE_NAME
def developBranch =  env.developBranch ?: 'develop'
def nodeLabel = env.nodeLabel ?: 'fleet-slave-node18'
def dockerImage = env.dockerImage ?: '************.dkr.ecr.ap-southeast-1.amazonaws.com/jenkins-build-agent:node18'
def buildCommand = env.buildCommand ?: "./build-all.sh"
def testCommand = env.testCommand ?: "./test-all.sh"
def deployCommand = env.deployCommand ?: "./deploy-all.sh"
def hostDeployCommand = env.hostDeployCommand ?: "echo 'do nothing.'"
def hostTestCommand = env.hostTestCommand ?: "echo 'do nothing.'"
def timeZone = env.timeZone ?: 'Asia/Hong_Kong'
def shouldPullDockerImage = env.shouldPullDockerImage == 'true'
def shouldBuild =  env.shouldSkipBuild != 'true'
def codeAnalysisCommand = env.codeAnalysisCommand ?: "./code_analysis.sh"
def configBranch = env.configBranch ?: 'master'
def releaseVersionId = env.releaseVersionId 

def repoParts = ENV_GIT_REPO.split('/');
for( String values : repoParts ) println(values);
println(repoParts[repoParts.length - 1]);
repoName = (repoParts[repoParts.length - 1]).split(/\./)[0];
def shouldRunCodeAnalysis = env.shouldRunCodeAnalysis.toBoolean()
def shouldRelease = false
def shouldTest = ENV_SKIP_TESTS != 'true'
echo "ENV_GIT_BRANCH = ${ENV_GIT_BRANCH}"
echo 'pull request info:'
echo "ENV_SOURCE_BRANCH = ${ENV_SOURCE_BRANCH}"
echo "ENV_TARGET_BRANCH = ${ENV_TARGET_BRANCH}"
echo "ENV_STATE = ${ENV_STATE}"

def ssh_private_key = ''

echo "shouldTest = ${shouldTest}"

// Release action condition
if (stageName) {
  echo "Triggered to release to stage: ${stageName}"
  shouldRelease = true
} else if (ENV_STATE == 'MERGED' && ENV_TARGET_BRANCH == developBranch) {
  echo "Triggered by pull request merged event, will be released to DEV"
  stageName = 'dev'
  shouldRelease = true
} else {
  stageName = 'dev2'
  echo "No build will be released"
}

if (ENV_STATE == 'MERGED' || env.shouldRunCodeAnalysis == 'true') {
  shouldRunCodeAnalysis = true
}

echo "stageName = ${stageName}"
echo "shouldRelease = ${shouldRelease}"
echo "repoName = ${repoName}"
echo "shouldRunCodeAnalysis = ${shouldRunCodeAnalysis}"

def aws(handle) {
  if (stageName == "live") {
    withAWS(roleAccount: "${ENV_ACCOUNT_ID}", role:'paris2-live-cross-account-access'){
      handle()
    }
  } else {
    withCredentials([[
    $class: 'AmazonWebServicesCredentialsBinding',
    credentialsId: "paris2_jenkins",
    accessKeyVariable: 'AWS_ACCESS_KEY_ID',
    secretKeyVariable: 'AWS_SECRET_ACCESS_KEY'
    ]]){
        handle()
    }
  }
}

def notifyBitbucket(stateName, onStage) {
  bitbucketStatusNotify(
    buildState: 'INPROGRESS',
    buildKey: stateName,
    buildName: stateName
  )

  try {
    onStage()
    bitbucketStatusNotify(
      buildState: 'SUCCESSFUL',
      buildKey: stateName,
      buildName: stateName
    )
  } catch (exc) {
    bitbucketStatusNotify(
      buildState: 'FAILED',
      buildKey: stateName,
      buildName: stateName,
      buildDescription: "${stateName} failed!!"
    )
    /* Rethrow to fail the Pipeline properly */
    throw exc
  }
}

def silent_sh(cmd) {
    sh('#!/bin/sh -e\n' + cmd)
}

pipeline {
    agent {
        label "${nodeLabel}"
    }
    environment {
      IS_PR_WEBHOOK = false // Default to false
    }

    stages {
        stage('Check if Triggered by PR Webhook') {
            steps {
                script {
                // Detect if the pipeline was triggered by a PR webhook
                if (ENV_STATE == 'OPEN' && ENV_SOURCE_BRANCH && ENV_TARGET_BRANCH) {
                    echo "Pipeline triggered by a PR webhook."
                    IS_PR_WEBHOOK = true
                    shouldTest = true
                    shouldRunCodeAnalysis = true
                    shouldBuild = true
                    shouldRelease = false
                } else {
                    echo "Pipeline was not triggered by a PR webhook."
                }
                }
            }
        }
                
        stage('Check Branch Name') {
            steps {
                script {
                    if (stageName == "live") {
                        // Check if ENV_GIT_BRANCH starts with refs/tags/
                        if (!ENV_GIT_BRANCH.startsWith('refs/tags/')) {
                            error("Deployment can only be triggered with tags. Please use tags for deployment.")
                        }
                    }
                }
            }
        }
        stage('Checkout') {
            steps {
                timeout(time: 1, unit: 'MINUTES'){
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: "${ENV_GIT_BRANCH}"]],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [], submoduleCfg: [],
                        userRemoteConfigs: [[
                        name: 'github',
                        credentialsId: 'fleet_devops',
                        url: "${ENV_GIT_REPO}"
                        ]]
                    ])

                    script {
                        aws {
                            sh "aws ecr get-login-password --region ${region} | docker login --username AWS --password-stdin ${accountId}.dkr.ecr.${region}.amazonaws.com"
                            ssh_private_key = readFile(file: '/home/<USER>/.ssh/id_rsa')
                        }
                    }
                }
            }
        }

        stage('Check Tag on Branch') {
            steps {
                script {
                    if (stageName == "live") {
                        sh "sudo git checkout master"
                        def branch = 'master'
                        
                        def result = sh(script: "git branch --contains \$(git rev-parse ${ENV_GIT_BRANCH}) | grep '^\\* ${branch}\$'", returnStatus: true)
                        
                        if (result == 0) {
                            echo "${ENV_GIT_BRANCH} is contained within ${branch}"
                            sh "sudo git checkout ${ENV_GIT_BRANCH}"
                        } else {
                            error("${ENV_GIT_BRANCH} is not contained within ${branch}")
                        }
                    } else {
                        echo "Skipping stage check Tag on Branch for ${stageName} "
                    }
                }
            }
        }

        stage('Pull docker image') {
            steps {
                timeout(time: 3, unit: 'MINUTES'){
                    script {
                        aws {
                            sh "docker pull ${dockerImage}"
                        }
                    }
                }
            }
        }

        stage('Test and deploy in container') {
            agent {
                docker {
                    image "${dockerImage}"
                    reuseNode true
                    args "--net=host -v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket:ro"
                }
            }

            environment {
                NPM_TOKEN = credentials('npmToken')
                SSH_PRIVATE_KEY = readFile(file: '/home/<USER>/.ssh/id_rsa')
                TIME_ZONE = "${timeZone}"
                ENV="${stageName}"
                BRANCH="${BRANCH}"
                TARGET_BRANCH="${ENV_TARGET_BRANCH}"
                CONFIG_BRANCH="${configBranch}"
            }

            stages {
                stage('Install Environment and build') {
                    when {
                        expression {
                            return shouldBuild;
                        }
                    }
                    steps {
                        timeout(time: 20, unit: 'MINUTES'){
                            script {
                                withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                                    sh "whoami"
                                    sh "cat ~/.npmrc"
                                    sh "echo ${NPM_TOKEN}"
                                    silent_sh "echo \"${SSH_PRIVATE_KEY}\" > \$HOME/.ssh/id_rsa"
                                    sh "chmod 600 \$HOME/.ssh/id_rsa"
                                    sh "npm i dotenv"
                                    sh "ENV=${ENV} ACCOUNT=${ACCOUNT} REGION=${REGION} ${buildCommand}"
                                    sh "cd npm-cdn && ./fetch.sh"
                                }
                            }
                        }
                    }
                }
            
                stage('Test') {
                    when {
                        expression {
                            return shouldTest;
                        }
                    }
                    steps {
                        script {
                            sh "TZ=${TIME_ZONE} ${testCommand}"
                        }
                    }
                }

                stage('Code Analysis') {
                when {
                    expression {
                    return shouldRunCodeAnalysis;
                    }
                }
                steps {
                    script {
                    echo 'Running Code Analysis stage'

                    // Set the projectKey based on releaseVersionId
                    def projectKey = releaseVersionId ? "paris2-web-base-${releaseVersionId}" : "paris2-web-base"
                    echo "Using projectKey: ${projectKey}"

                    def runAnalysis = { scriptPath ->
                        sh "${scriptPath}"

                        // Fetch SonarQube quality gate status
                        def SONAR_AUTH_TOKEN = sh(
                        script: "aws ssm get-parameter --name '/paris2-sonar-auth-token/${stageName}' --with-decryption --query 'Parameter.Value' --output text",
                        returnStdout: true
                        ).trim()

                        echo "Sonar Token fetched: ${SONAR_AUTH_TOKEN}"

                        def QUALITY_GATE_STATUS = sh(
                        script: """curl -s -u ${SONAR_AUTH_TOKEN}: \
                        "https://sonar-dev.fleetship.com/api/qualitygates/project_status?projectKey=${projectKey}" \
                        | jq -r '.projectStatus.status'""",
                        returnStdout: true
                        ).trim()

                        if ("${QUALITY_GATE_STATUS}" != "OK") {
                        currentBuild.result = 'UNSTABLE'
                        bitbucketStatusNotify(
                            buildState: 'FAILED',
                            buildKey: 'Sonar analysis',
                            buildName: 'Sonar analysis',
                            buildDescription: "Sonar analysis marked build as unstable."
                        )
                        return
                        } else {
                        echo "SonarQube quality gate passed!"
                        notifyBitbucket('Sonar analysis Passed') {
                            echo "Reporting to Bitbucket..."
                        }
                        }
                    }

                    aws {
                        withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                        try {
                            if (codeAnalysisCommand == "./code_analysis.sh") {
                            echo 'Checking ./code_analysis.sh'
                            def isCodeAnalysisScriptExist = sh script: 'test -f ./code_analysis.sh', returnStatus: true
                            
                            echo "isCodeAnalysisScriptExist: ${isCodeAnalysisScriptExist}"
                            if (isCodeAnalysisScriptExist == 0) {
                                runAnalysis("./code_analysis.sh")
                            } else {
                                echo 'No code_analysis.sh found, skipping it'
                            }
                            } else {
                            echo 'Running codeAnalysisCommand directly'
                            runAnalysis(codeAnalysisCommand)
                            }
                        } catch (Exception e) {
                            echo "Caught an exception during Sonar Analysis, marking as unstable"
                            bitbucketStatusNotify(
                            buildState: 'FAILED',
                            buildKey: 'Sonar analysis',
                            buildName: 'Sonar analysis',
                            buildDescription: "Sonar analysis marked build as unstable."
                            )
                            currentBuild.result = 'UNSTABLE'
                        }
                        }
                    }
                    }
                }
                }
                stage('Deploy') {
                    when {
                        expression {
                            return shouldRelease;
                        }
                    }
                    steps {
                        timeout(time: 1, unit: 'MINUTES'){
                            script {
                                aws{
                                    sh "${deployCommand}"
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
    }
}