@import '~react-datepicker/dist/react-datepicker.css';
@import './variable';
@import './styles.scss';

// ###### App
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  font-family: $font-family;
  font-size: $root-font-size;
  height: 100%;
}

body {
  background-color: #ffffff;
  margin-top: 80px;
  color: $text-color;
  overflow-x: hidden;

  [id='single-spa-application:@paris2/risk-assessment'] {
    height: 100%;
    padding: 0px 15px;
    padding-bottom: 20px;

    &:empty {
      height: auto;
      padding: 0px;
    }
  }

  a {
    color: $primary-color;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #b8b8b8; /* Scrollbar thumb color */
    border-radius: 4px; /* Round the corners of the scrollbar thumb */
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #656565; /* Scrollbar thumb color on hover */
  }

  .ra-three-dots-dropdown {
    .dropdown-toggle-no-caret {
      cursor: pointer;
      width: 20px;
      height: 20px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;

      &::after {
        display: none; // Remove the default caret
      }

      &:hover,
      &:focus,
      &:active {
        background-color: #edf3f7;
        color: #1c1f4a;
      }
    }
    // Apply styling when dropdown is open
    &.show .dropdown-toggle-no-caret {
      background-color: #edf3f7;
      color: #1c1f4a;
    }

    .dropdown-menu {
      min-width: 124px;
      border-radius: 4px;
      border: 1px solid #cccccc;
      padding: 8px;
      z-index: 2000 !important;
      position: absolute !important;
      pointer-events: auto; // ensure menu is interactive
      transition: box-shadow 0.2s;

      .dropdown-item {
        font-size: 14px;
        padding: 4px 8px;

        &:hover,
        &:focus {
          background-color: #f8f9fa;
        }
      }

      &:hover,
      &:focus-within {
        z-index: 2000 !important;
        position: absolute !important;
        box-shadow: 0 4px 16px rgba(60, 60, 60, 0.15);
        border-color: #1c1f4a;
        background: #f5f8fa;
      }
    }
  }

  td:has(.ra-three-dots-dropdown.show) {
    z-index: 10;
  }

  .infinite-scroll-table-container {
    overflow: visible !important;
    position: relative;
    z-index: 1;
  }

  .table-responsive,
  .table,
  tbody,
  tr {
    overflow: visible !important;
  }
}

// Sticky column z-index rules
td.sticky-styles-right,
th.sticky-styles-right {
  z-index: 5;
}

// Robust: keep sticky cell above when dropdown or menu is open (for browsers that support :has)
td.sticky-styles-right:has(.ra-three-dots-dropdown.show),
th.sticky-styles-right:has(.ra-three-dots-dropdown.show),
td.sticky-styles-right:has(.ra-three-dots-dropdown .dropdown-menu.show),
th.sticky-styles-right:has(.ra-three-dots-dropdown .dropdown-menu.show) {
  z-index: 2001 !important;
}

// Fallback for all browsers: use a class
td.sticky-styles-right.dropdown-open,
th.sticky-styles-right.dropdown-open {
  z-index: 2001 !important;
  border-right: 2px solid #1c1f4a;
  background: #f5f8fa;
}

.ra-three-dots-dropdown .dropdown-toggle-no-caret:hover,
.ra-three-dots-dropdown .dropdown-toggle-no-caret:focus,
.ra-three-dots-dropdown .dropdown-toggle-no-caret:active {
  background-color: #e0e7ef;
  color: #1c1f4a;
  box-shadow: 0 2px 8px rgba(60, 60, 60, 0.10);
}
