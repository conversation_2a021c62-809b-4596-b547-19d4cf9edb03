import UserRoleController from '../../src/controller/user-role-controller';
import {RISK_ASSESMENT_ROLES as ROLES} from '../../src/constants/roles';
import {JwtUser} from '../../src/types/user';

describe('UserRoleController', () => {
  let userRoleController: UserRoleController;

  beforeEach(() => {
    userRoleController = new UserRoleController();
  });

  describe('getConfig', () => {
    const createMockKeycloakContext = (
      roles: string[] = [],
      tokenParsed: Partial<JwtUser> = {},
    ) => ({
      realmAccess: {
        roles,
      },
      tokenParsed: {
        sub: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        preferred_username: 'testuser',
        given_name: 'Test',
        family_name: 'User',
        user_id: 'test-user-id',
        exp: **********,
        iat: **********,
        auth_time: **********,
        jti: 'test-jti',
        iss: 'test-issuer',
        aud: 'test-audience',
        typ: 'Bearer',
        azp: 'test-azp',
        nonce: 'test-nonce',
        session_state: 'test-session',
        acr: '1',
        'allowed-origins': ['http://localhost'],
        realm_access: {roles},
        resource_access: {
          account: {roles: []},
        },
        scope: 'openid profile email',
        user_name_hash: 'test-hash',
        email_verified: true,
        is_nova_onboarded: true,
        tc_nova_version: 1,
        is_user_onboarded: true,
        group: ['test-group'],
        ...tokenParsed,
      },
    });

    describe('Basic functionality', () => {
      it('should return config with user and riskAssessment properties', () => {
        const mockKc = createMockKeycloakContext();
        const config = userRoleController.getConfig(mockKc);

        expect(config).toHaveProperty('user');
        expect(config).toHaveProperty('riskAssessment');
        expect(config.user).toBe(mockKc.tokenParsed);
      });

      it('should return user from tokenParsed', () => {
        const mockUser = {
          sub: 'custom-user-id',
          email: '<EMAIL>',
          name: 'Custom User',
          preferred_username: 'customuser',
        };
        const mockKc = createMockKeycloakContext([], mockUser);
        const config = userRoleController.getConfig(mockKc);

        expect(config.user).toEqual(expect.objectContaining(mockUser));
      });
    });

    describe('Role-based permissions', () => {
      describe('canCreateNewTemplate permission', () => {
        it('should return true when user has RA_CREATE_TEMPLATE_ACCESS role', () => {
          const mockKc = createMockKeycloakContext([
            ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS,
          ]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.canCreateNewTemplate).toBe(true);
        });

        it('should return false when user does not have RA_CREATE_TEMPLATE_ACCESS role', () => {
          const mockKc = createMockKeycloakContext(['some-other-role']);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.canCreateNewTemplate).toBe(false);
        });

        it('should return false when user has no roles', () => {
          const mockKc = createMockKeycloakContext([]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.canCreateNewTemplate).toBe(false);
        });
      });

      describe('hasPermision (view access)', () => {
        it('should return true when user has RA_VIEW_ACCESS role', () => {
          const mockKc = createMockKeycloakContext([ROLES.HAS_RA_VIEW_ACCESS]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(true);
        });

        it('should return false when user does not have RA_VIEW_ACCESS role', () => {
          const mockKc = createMockKeycloakContext(['some-other-role']);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(false);
        });

        it('should return false when user has no roles', () => {
          const mockKc = createMockKeycloakContext([]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(false);
        });
      });

      describe('Multiple roles scenarios', () => {
        it('should handle user with both create and view permissions', () => {
          const mockKc = createMockKeycloakContext([
            ROLES.HAS_RA_VIEW_ACCESS,
            ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS,
          ]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(true);
          expect(config.riskAssessment.canCreateNewTemplate).toBe(true);
        });

        it('should handle user with only view permission', () => {
          const mockKc = createMockKeycloakContext([ROLES.HAS_RA_VIEW_ACCESS]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(true);
          expect(config.riskAssessment.canCreateNewTemplate).toBe(false);
        });

        it('should handle user with only create permission', () => {
          const mockKc = createMockKeycloakContext([
            ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS,
          ]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(false);
          expect(config.riskAssessment.canCreateNewTemplate).toBe(true);
        });

        it('should handle user with multiple unrelated roles', () => {
          const mockKc = createMockKeycloakContext([
            'unrelated-role-1',
            'unrelated-role-2',
            ROLES.HAS_RA_VIEW_ACCESS,
            'another-unrelated-role',
          ]);
          const config = userRoleController.getConfig(mockKc);

          expect(config.riskAssessment.hasPermision).toBe(true);
          expect(config.riskAssessment.canCreateNewTemplate).toBe(false);
        });
      });
    });

    describe('Edge cases and error handling', () => {
      it('should handle missing realmAccess', () => {
        const mockKc = {
          tokenParsed: {
            sub: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
          },
        };

        expect(() => userRoleController.getConfig(mockKc)).toThrow();
      });

      it('should handle missing realmAccess.roles', () => {
        const mockKc = {
          realmAccess: {},
          tokenParsed: {
            sub: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
          },
        };

        expect(() => userRoleController.getConfig(mockKc)).toThrow();
      });

      it('should handle null/undefined roles array', () => {
        const mockKc = {
          realmAccess: {
            roles: null,
          },
          tokenParsed: {
            sub: 'test-user',
            email: '<EMAIL>',
            name: 'Test User',
          },
        };

        expect(() => userRoleController.getConfig(mockKc)).toThrow();
      });

      it('should handle empty tokenParsed', () => {
        const mockKc = createMockKeycloakContext(
          [ROLES.HAS_RA_VIEW_ACCESS],
          {},
        );
        const config = userRoleController.getConfig(mockKc);

        expect(config.user).toBeDefined();
        expect(config.riskAssessment.hasPermision).toBe(true);
      });

      it('should handle null tokenParsed', () => {
        const mockKc = {
          realmAccess: {
            roles: [ROLES.HAS_RA_VIEW_ACCESS],
          },
          tokenParsed: null,
        };
        const config = userRoleController.getConfig(mockKc);

        expect(config.user).toBeNull();
        expect(config.riskAssessment.hasPermision).toBe(true);
      });
    });

    describe('Role constants validation', () => {
      it('should use correct role constants', () => {
        expect(ROLES.HAS_RA_VIEW_ACCESS).toBe('ra|temp|view');
        expect(ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS).toBe('ra|temp|create');
      });

      it('should work with actual role constant values', () => {
        const mockKc = createMockKeycloakContext([
          'ra|temp|view',
          'ra|temp|create',
        ]);
        const config = userRoleController.getConfig(mockKc);

        expect(config.riskAssessment.hasPermision).toBe(true);
        expect(config.riskAssessment.canCreateNewTemplate).toBe(true);
      });
    });

    describe('Return type validation', () => {
      it('should return object with correct structure', () => {
        const mockKc = createMockKeycloakContext([ROLES.HAS_RA_VIEW_ACCESS]);
        const config = userRoleController.getConfig(mockKc);

        expect(typeof config).toBe('object');
        expect(config).toHaveProperty('user');
        expect(config).toHaveProperty('riskAssessment');
        expect(typeof config.riskAssessment).toBe('object');
        expect(config.riskAssessment).toHaveProperty('canCreateNewTemplate');
        expect(config.riskAssessment).toHaveProperty('hasPermision');
        expect(typeof config.riskAssessment.canCreateNewTemplate).toBe(
          'boolean',
        );
        expect(typeof config.riskAssessment.hasPermision).toBe('boolean');
      });

      it('should return consistent results for same input', () => {
        const mockKc = createMockKeycloakContext([ROLES.HAS_RA_VIEW_ACCESS]);
        const config1 = userRoleController.getConfig(mockKc);
        const config2 = userRoleController.getConfig(mockKc);

        expect(config1).toEqual(config2);
      });
    });

    describe('Integration scenarios', () => {
      it('should work with realistic user data', () => {
        const realisticUser = {
          sub: '12345678-1234-1234-1234-**********12',
          email: '<EMAIL>',
          name: 'John Doe',
          preferred_username: 'john.doe',
          given_name: 'John',
          family_name: 'Doe',
          user_id: 'john.doe',
          email_verified: true,
          is_nova_onboarded: true,
          is_user_onboarded: true,
          group: ['engineering', 'risk-assessment'],
        };

        const mockKc = createMockKeycloakContext(
          [
            ROLES.HAS_RA_VIEW_ACCESS,
            ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS,
            'other-role',
          ],
          realisticUser,
        );
        const config = userRoleController.getConfig(mockKc);

        expect(config.user).toEqual(expect.objectContaining(realisticUser));
        expect(config.riskAssessment.hasPermision).toBe(true);
        expect(config.riskAssessment.canCreateNewTemplate).toBe(true);
      });

      it('should handle case-sensitive role matching', () => {
        const mockKc = createMockKeycloakContext([
          'RA|TEMP|VIEW',
          'ra|temp|create',
        ]);
        const config = userRoleController.getConfig(mockKc);

        // Role matching should be case-sensitive
        expect(config.riskAssessment.hasPermision).toBe(false);
        expect(config.riskAssessment.canCreateNewTemplate).toBe(true);
      });
    });
  });

  describe('Class instantiation', () => {
    it('should create instance successfully', () => {
      const controller = new UserRoleController();
      expect(controller).toBeInstanceOf(UserRoleController);
      expect(typeof controller.getConfig).toBe('function');
    });

    it('should create multiple independent instances', () => {
      const controller1 = new UserRoleController();
      const controller2 = new UserRoleController();

      expect(controller1).toBeInstanceOf(UserRoleController);
      expect(controller2).toBeInstanceOf(UserRoleController);
      expect(controller1).not.toBe(controller2);
    });
  });
});
